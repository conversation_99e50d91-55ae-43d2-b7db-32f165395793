/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeSubnamespaceAnchors implements SubnamespaceAnchorInterface
type FakeSubnamespaceAnchors struct {
	Fake *FakeHncV1alpha2
	ns   string
}

var subnamespaceanchorsResource = schema.GroupVersionResource{Group: "hnc.x-k8s.io", Version: "v1alpha2", Resource: "subnamespaceanchors"}

var subnamespaceanchorsKind = schema.GroupVersionKind{Group: "hnc.x-k8s.io", Version: "v1alpha2", Kind: "SubnamespaceAnchor"}

// Get takes name of the subnamespaceAnchor, and returns the corresponding subnamespaceAnchor object, and an error if there is any.
func (c *FakeSubnamespaceAnchors) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha2.SubnamespaceAnchor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(subnamespaceanchorsResource, c.ns, name), &v1alpha2.SubnamespaceAnchor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.SubnamespaceAnchor), err
}

// List takes label and field selectors, and returns the list of SubnamespaceAnchors that match those selectors.
func (c *FakeSubnamespaceAnchors) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha2.SubnamespaceAnchorList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(subnamespaceanchorsResource, subnamespaceanchorsKind, c.ns, opts), &v1alpha2.SubnamespaceAnchorList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha2.SubnamespaceAnchorList{ListMeta: obj.(*v1alpha2.SubnamespaceAnchorList).ListMeta}
	for _, item := range obj.(*v1alpha2.SubnamespaceAnchorList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested subnamespaceAnchors.
func (c *FakeSubnamespaceAnchors) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(subnamespaceanchorsResource, c.ns, opts))

}

// Create takes the representation of a subnamespaceAnchor and creates it.  Returns the server's representation of the subnamespaceAnchor, and an error, if there is any.
func (c *FakeSubnamespaceAnchors) Create(ctx context.Context, subnamespaceAnchor *v1alpha2.SubnamespaceAnchor, opts v1.CreateOptions) (result *v1alpha2.SubnamespaceAnchor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(subnamespaceanchorsResource, c.ns, subnamespaceAnchor), &v1alpha2.SubnamespaceAnchor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.SubnamespaceAnchor), err
}

// Update takes the representation of a subnamespaceAnchor and updates it. Returns the server's representation of the subnamespaceAnchor, and an error, if there is any.
func (c *FakeSubnamespaceAnchors) Update(ctx context.Context, subnamespaceAnchor *v1alpha2.SubnamespaceAnchor, opts v1.UpdateOptions) (result *v1alpha2.SubnamespaceAnchor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(subnamespaceanchorsResource, c.ns, subnamespaceAnchor), &v1alpha2.SubnamespaceAnchor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.SubnamespaceAnchor), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeSubnamespaceAnchors) UpdateStatus(ctx context.Context, subnamespaceAnchor *v1alpha2.SubnamespaceAnchor, opts v1.UpdateOptions) (*v1alpha2.SubnamespaceAnchor, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(subnamespaceanchorsResource, "status", c.ns, subnamespaceAnchor), &v1alpha2.SubnamespaceAnchor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.SubnamespaceAnchor), err
}

// Delete takes name of the subnamespaceAnchor and deletes it. Returns an error if one occurs.
func (c *FakeSubnamespaceAnchors) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(subnamespaceanchorsResource, c.ns, name, opts), &v1alpha2.SubnamespaceAnchor{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeSubnamespaceAnchors) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(subnamespaceanchorsResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha2.SubnamespaceAnchorList{})
	return err
}

// Patch applies the patch and returns the patched subnamespaceAnchor.
func (c *FakeSubnamespaceAnchors) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha2.SubnamespaceAnchor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(subnamespaceanchorsResource, c.ns, name, pt, data, subresources...), &v1alpha2.SubnamespaceAnchor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.SubnamespaceAnchor), err
}
