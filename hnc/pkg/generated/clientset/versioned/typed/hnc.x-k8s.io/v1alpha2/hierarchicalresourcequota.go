/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1alpha2

import (
	"context"
	"time"

	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	scheme "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// HierarchicalResourceQuotasGetter has a method to return a HierarchicalResourceQuotaInterface.
// A group's client should implement this interface.
type HierarchicalResourceQuotasGetter interface {
	HierarchicalResourceQuotas(namespace string) HierarchicalResourceQuotaInterface
}

// HierarchicalResourceQuotaInterface has methods to work with HierarchicalResourceQuota resources.
type HierarchicalResourceQuotaInterface interface {
	Create(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.CreateOptions) (*v1alpha2.HierarchicalResourceQuota, error)
	Update(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.UpdateOptions) (*v1alpha2.HierarchicalResourceQuota, error)
	UpdateStatus(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.UpdateOptions) (*v1alpha2.HierarchicalResourceQuota, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha2.HierarchicalResourceQuota, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha2.HierarchicalResourceQuotaList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha2.HierarchicalResourceQuota, err error)
	HierarchicalResourceQuotaExpansion
}

// hierarchicalResourceQuotas implements HierarchicalResourceQuotaInterface
type hierarchicalResourceQuotas struct {
	client rest.Interface
	ns     string
}

// newHierarchicalResourceQuotas returns a HierarchicalResourceQuotas
func newHierarchicalResourceQuotas(c *HncV1alpha2Client, namespace string) *hierarchicalResourceQuotas {
	return &hierarchicalResourceQuotas{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the hierarchicalResourceQuota, and returns the corresponding hierarchicalResourceQuota object, and an error if there is any.
func (c *hierarchicalResourceQuotas) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	result = &v1alpha2.HierarchicalResourceQuota{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of HierarchicalResourceQuotas that match those selectors.
func (c *hierarchicalResourceQuotas) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha2.HierarchicalResourceQuotaList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha2.HierarchicalResourceQuotaList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested hierarchicalResourceQuotas.
func (c *hierarchicalResourceQuotas) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a hierarchicalResourceQuota and creates it.  Returns the server's representation of the hierarchicalResourceQuota, and an error, if there is any.
func (c *hierarchicalResourceQuotas) Create(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.CreateOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	result = &v1alpha2.HierarchicalResourceQuota{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(hierarchicalResourceQuota).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a hierarchicalResourceQuota and updates it. Returns the server's representation of the hierarchicalResourceQuota, and an error, if there is any.
func (c *hierarchicalResourceQuotas) Update(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.UpdateOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	result = &v1alpha2.HierarchicalResourceQuota{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		Name(hierarchicalResourceQuota.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(hierarchicalResourceQuota).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *hierarchicalResourceQuotas) UpdateStatus(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.UpdateOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	result = &v1alpha2.HierarchicalResourceQuota{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		Name(hierarchicalResourceQuota.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(hierarchicalResourceQuota).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the hierarchicalResourceQuota and deletes it. Returns an error if one occurs.
func (c *hierarchicalResourceQuotas) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *hierarchicalResourceQuotas) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched hierarchicalResourceQuota.
func (c *hierarchicalResourceQuotas) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	result = &v1alpha2.HierarchicalResourceQuota{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("hierarchicalresourcequotas").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
