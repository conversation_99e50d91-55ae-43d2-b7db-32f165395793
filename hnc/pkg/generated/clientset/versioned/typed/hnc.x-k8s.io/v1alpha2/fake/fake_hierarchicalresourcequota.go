/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeHierarchicalResourceQuotas implements HierarchicalResourceQuotaInterface
type FakeHierarchicalResourceQuotas struct {
	Fake *FakeHncV1alpha2
	ns   string
}

var hierarchicalresourcequotasResource = schema.GroupVersionResource{Group: "hnc.x-k8s.io", Version: "v1alpha2", Resource: "hierarchicalresourcequotas"}

var hierarchicalresourcequotasKind = schema.GroupVersionKind{Group: "hnc.x-k8s.io", Version: "v1alpha2", Kind: "HierarchicalResourceQuota"}

// Get takes name of the hierarchicalResourceQuota, and returns the corresponding hierarchicalResourceQuota object, and an error if there is any.
func (c *FakeHierarchicalResourceQuotas) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(hierarchicalresourcequotasResource, c.ns, name), &v1alpha2.HierarchicalResourceQuota{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.HierarchicalResourceQuota), err
}

// List takes label and field selectors, and returns the list of HierarchicalResourceQuotas that match those selectors.
func (c *FakeHierarchicalResourceQuotas) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha2.HierarchicalResourceQuotaList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(hierarchicalresourcequotasResource, hierarchicalresourcequotasKind, c.ns, opts), &v1alpha2.HierarchicalResourceQuotaList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha2.HierarchicalResourceQuotaList{ListMeta: obj.(*v1alpha2.HierarchicalResourceQuotaList).ListMeta}
	for _, item := range obj.(*v1alpha2.HierarchicalResourceQuotaList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested hierarchicalResourceQuotas.
func (c *FakeHierarchicalResourceQuotas) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(hierarchicalresourcequotasResource, c.ns, opts))

}

// Create takes the representation of a hierarchicalResourceQuota and creates it.  Returns the server's representation of the hierarchicalResourceQuota, and an error, if there is any.
func (c *FakeHierarchicalResourceQuotas) Create(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.CreateOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(hierarchicalresourcequotasResource, c.ns, hierarchicalResourceQuota), &v1alpha2.HierarchicalResourceQuota{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.HierarchicalResourceQuota), err
}

// Update takes the representation of a hierarchicalResourceQuota and updates it. Returns the server's representation of the hierarchicalResourceQuota, and an error, if there is any.
func (c *FakeHierarchicalResourceQuotas) Update(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.UpdateOptions) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(hierarchicalresourcequotasResource, c.ns, hierarchicalResourceQuota), &v1alpha2.HierarchicalResourceQuota{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.HierarchicalResourceQuota), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeHierarchicalResourceQuotas) UpdateStatus(ctx context.Context, hierarchicalResourceQuota *v1alpha2.HierarchicalResourceQuota, opts v1.UpdateOptions) (*v1alpha2.HierarchicalResourceQuota, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(hierarchicalresourcequotasResource, "status", c.ns, hierarchicalResourceQuota), &v1alpha2.HierarchicalResourceQuota{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.HierarchicalResourceQuota), err
}

// Delete takes name of the hierarchicalResourceQuota and deletes it. Returns an error if one occurs.
func (c *FakeHierarchicalResourceQuotas) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(hierarchicalresourcequotasResource, c.ns, name, opts), &v1alpha2.HierarchicalResourceQuota{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeHierarchicalResourceQuotas) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(hierarchicalresourcequotasResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha2.HierarchicalResourceQuotaList{})
	return err
}

// Patch applies the patch and returns the patched hierarchicalResourceQuota.
func (c *FakeHierarchicalResourceQuotas) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha2.HierarchicalResourceQuota, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(hierarchicalresourcequotasResource, c.ns, name, pt, data, subresources...), &v1alpha2.HierarchicalResourceQuota{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.HierarchicalResourceQuota), err
}
