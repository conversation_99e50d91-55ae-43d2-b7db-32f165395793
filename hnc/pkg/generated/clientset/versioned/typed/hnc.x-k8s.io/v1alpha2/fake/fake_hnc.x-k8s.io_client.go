/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/clientset/versioned/typed/hnc.x-k8s.io/v1alpha2"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeHncV1alpha2 struct {
	*testing.Fake
}

func (c *FakeHncV1alpha2) HierarchicalResourceQuotas(namespace string) v1alpha2.HierarchicalResourceQuotaInterface {
	return &FakeHierarchicalResourceQuotas{c, namespace}
}

func (c *FakeHncV1alpha2) HierarchyConfigurations(namespace string) v1alpha2.HierarchyConfigurationInterface {
	return &FakeHierarchyConfigurations{c, namespace}
}

func (c *FakeHncV1alpha2) SubnamespaceAnchors(namespace string) v1alpha2.SubnamespaceAnchorInterface {
	return &FakeSubnamespaceAnchors{c, namespace}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeHncV1alpha2) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
