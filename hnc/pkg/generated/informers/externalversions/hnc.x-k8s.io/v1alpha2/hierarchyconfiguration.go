/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by informer-gen. DO NOT EDIT.

package v1alpha2

import (
	"context"
	time "time"

	hncxk8siov1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	versioned "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/clientset/versioned"
	internalinterfaces "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/informers/externalversions/internalinterfaces"
	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/listers/hnc.x-k8s.io/v1alpha2"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// HierarchyConfigurationInformer provides access to a shared informer and lister for
// HierarchyConfigurations.
type HierarchyConfigurationInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1alpha2.HierarchyConfigurationLister
}

type hierarchyConfigurationInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewHierarchyConfigurationInformer constructs a new informer for HierarchyConfiguration type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewHierarchyConfigurationInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredHierarchyConfigurationInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredHierarchyConfigurationInformer constructs a new informer for HierarchyConfiguration type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredHierarchyConfigurationInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.HncV1alpha2().HierarchyConfigurations(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.HncV1alpha2().HierarchyConfigurations(namespace).Watch(context.TODO(), options)
			},
		},
		&hncxk8siov1alpha2.HierarchyConfiguration{},
		resyncPeriod,
		indexers,
	)
}

func (f *hierarchyConfigurationInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredHierarchyConfigurationInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *hierarchyConfigurationInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&hncxk8siov1alpha2.HierarchyConfiguration{}, f.defaultInformer)
}

func (f *hierarchyConfigurationInformer) Lister() v1alpha2.HierarchyConfigurationLister {
	return v1alpha2.NewHierarchyConfigurationLister(f.Informer().GetIndexer())
}
