/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by informer-gen. DO NOT EDIT.

package v1alpha2

import (
	internalinterfaces "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// HierarchicalResourceQuotas returns a HierarchicalResourceQuotaInformer.
	HierarchicalResourceQuotas() HierarchicalResourceQuotaInformer
	// HierarchyConfigurations returns a HierarchyConfigurationInformer.
	HierarchyConfigurations() HierarchyConfigurationInformer
	// SubnamespaceAnchors returns a SubnamespaceAnchorInformer.
	SubnamespaceAnchors() SubnamespaceAnchorInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// HierarchicalResourceQuotas returns a HierarchicalResourceQuotaInformer.
func (v *version) HierarchicalResourceQuotas() HierarchicalResourceQuotaInformer {
	return &hierarchicalResourceQuotaInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// HierarchyConfigurations returns a HierarchyConfigurationInformer.
func (v *version) HierarchyConfigurations() HierarchyConfigurationInformer {
	return &hierarchyConfigurationInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// SubnamespaceAnchors returns a SubnamespaceAnchorInformer.
func (v *version) SubnamespaceAnchors() SubnamespaceAnchorInformer {
	return &subnamespaceAnchorInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}
