/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by lister-gen. DO NOT EDIT.

package v1alpha2

import (
	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// HierarchicalResourceQuotaLister helps list HierarchicalResourceQuotas.
// All objects returned here must be treated as read-only.
type HierarchicalResourceQuotaLister interface {
	// List lists all HierarchicalResourceQuotas in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha2.HierarchicalResourceQuota, err error)
	// HierarchicalResourceQuotas returns an object that can list and get HierarchicalResourceQuotas.
	HierarchicalResourceQuotas(namespace string) HierarchicalResourceQuotaNamespaceLister
	HierarchicalResourceQuotaListerExpansion
}

// hierarchicalResourceQuotaLister implements the HierarchicalResourceQuotaLister interface.
type hierarchicalResourceQuotaLister struct {
	indexer cache.Indexer
}

// NewHierarchicalResourceQuotaLister returns a new HierarchicalResourceQuotaLister.
func NewHierarchicalResourceQuotaLister(indexer cache.Indexer) HierarchicalResourceQuotaLister {
	return &hierarchicalResourceQuotaLister{indexer: indexer}
}

// List lists all HierarchicalResourceQuotas in the indexer.
func (s *hierarchicalResourceQuotaLister) List(selector labels.Selector) (ret []*v1alpha2.HierarchicalResourceQuota, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha2.HierarchicalResourceQuota))
	})
	return ret, err
}

// HierarchicalResourceQuotas returns an object that can list and get HierarchicalResourceQuotas.
func (s *hierarchicalResourceQuotaLister) HierarchicalResourceQuotas(namespace string) HierarchicalResourceQuotaNamespaceLister {
	return hierarchicalResourceQuotaNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// HierarchicalResourceQuotaNamespaceLister helps list and get HierarchicalResourceQuotas.
// All objects returned here must be treated as read-only.
type HierarchicalResourceQuotaNamespaceLister interface {
	// List lists all HierarchicalResourceQuotas in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha2.HierarchicalResourceQuota, err error)
	// Get retrieves the HierarchicalResourceQuota from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha2.HierarchicalResourceQuota, error)
	HierarchicalResourceQuotaNamespaceListerExpansion
}

// hierarchicalResourceQuotaNamespaceLister implements the HierarchicalResourceQuotaNamespaceLister
// interface.
type hierarchicalResourceQuotaNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all HierarchicalResourceQuotas in the indexer for a given namespace.
func (s hierarchicalResourceQuotaNamespaceLister) List(selector labels.Selector) (ret []*v1alpha2.HierarchicalResourceQuota, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha2.HierarchicalResourceQuota))
	})
	return ret, err
}

// Get retrieves the HierarchicalResourceQuota from the indexer for a given namespace and name.
func (s hierarchicalResourceQuotaNamespaceLister) Get(name string) (*v1alpha2.HierarchicalResourceQuota, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha2.Resource("hierarchicalresourcequota"), name)
	}
	return obj.(*v1alpha2.HierarchicalResourceQuota), nil
}
