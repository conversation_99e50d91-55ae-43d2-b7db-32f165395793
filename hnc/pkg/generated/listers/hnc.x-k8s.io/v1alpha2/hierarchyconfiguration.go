/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by lister-gen. DO NOT EDIT.

package v1alpha2

import (
	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// HierarchyConfigurationLister helps list HierarchyConfigurations.
// All objects returned here must be treated as read-only.
type HierarchyConfigurationLister interface {
	// List lists all HierarchyConfigurations in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha2.HierarchyConfiguration, err error)
	// HierarchyConfigurations returns an object that can list and get HierarchyConfigurations.
	HierarchyConfigurations(namespace string) HierarchyConfigurationNamespaceLister
	HierarchyConfigurationListerExpansion
}

// hierarchyConfigurationLister implements the HierarchyConfigurationLister interface.
type hierarchyConfigurationLister struct {
	indexer cache.Indexer
}

// NewHierarchyConfigurationLister returns a new HierarchyConfigurationLister.
func NewHierarchyConfigurationLister(indexer cache.Indexer) HierarchyConfigurationLister {
	return &hierarchyConfigurationLister{indexer: indexer}
}

// List lists all HierarchyConfigurations in the indexer.
func (s *hierarchyConfigurationLister) List(selector labels.Selector) (ret []*v1alpha2.HierarchyConfiguration, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha2.HierarchyConfiguration))
	})
	return ret, err
}

// HierarchyConfigurations returns an object that can list and get HierarchyConfigurations.
func (s *hierarchyConfigurationLister) HierarchyConfigurations(namespace string) HierarchyConfigurationNamespaceLister {
	return hierarchyConfigurationNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// HierarchyConfigurationNamespaceLister helps list and get HierarchyConfigurations.
// All objects returned here must be treated as read-only.
type HierarchyConfigurationNamespaceLister interface {
	// List lists all HierarchyConfigurations in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha2.HierarchyConfiguration, err error)
	// Get retrieves the HierarchyConfiguration from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha2.HierarchyConfiguration, error)
	HierarchyConfigurationNamespaceListerExpansion
}

// hierarchyConfigurationNamespaceLister implements the HierarchyConfigurationNamespaceLister
// interface.
type hierarchyConfigurationNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all HierarchyConfigurations in the indexer for a given namespace.
func (s hierarchyConfigurationNamespaceLister) List(selector labels.Selector) (ret []*v1alpha2.HierarchyConfiguration, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha2.HierarchyConfiguration))
	})
	return ret, err
}

// Get retrieves the HierarchyConfiguration from the indexer for a given namespace and name.
func (s hierarchyConfigurationNamespaceLister) Get(name string) (*v1alpha2.HierarchyConfiguration, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha2.Resource("hierarchyconfiguration"), name)
	}
	return obj.(*v1alpha2.HierarchyConfiguration), nil
}
