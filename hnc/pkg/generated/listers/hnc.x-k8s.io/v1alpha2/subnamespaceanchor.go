/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by lister-gen. DO NOT EDIT.

package v1alpha2

import (
	v1alpha2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// SubnamespaceAnchorLister helps list SubnamespaceAnchors.
// All objects returned here must be treated as read-only.
type SubnamespaceAnchorLister interface {
	// List lists all SubnamespaceAnchors in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha2.SubnamespaceAnchor, err error)
	// SubnamespaceAnchors returns an object that can list and get SubnamespaceAnchors.
	SubnamespaceAnchors(namespace string) SubnamespaceAnchorNamespaceLister
	SubnamespaceAnchorListerExpansion
}

// subnamespaceAnchorLister implements the SubnamespaceAnchorLister interface.
type subnamespaceAnchorLister struct {
	indexer cache.Indexer
}

// NewSubnamespaceAnchorLister returns a new SubnamespaceAnchorLister.
func NewSubnamespaceAnchorLister(indexer cache.Indexer) SubnamespaceAnchorLister {
	return &subnamespaceAnchorLister{indexer: indexer}
}

// List lists all SubnamespaceAnchors in the indexer.
func (s *subnamespaceAnchorLister) List(selector labels.Selector) (ret []*v1alpha2.SubnamespaceAnchor, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha2.SubnamespaceAnchor))
	})
	return ret, err
}

// SubnamespaceAnchors returns an object that can list and get SubnamespaceAnchors.
func (s *subnamespaceAnchorLister) SubnamespaceAnchors(namespace string) SubnamespaceAnchorNamespaceLister {
	return subnamespaceAnchorNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// SubnamespaceAnchorNamespaceLister helps list and get SubnamespaceAnchors.
// All objects returned here must be treated as read-only.
type SubnamespaceAnchorNamespaceLister interface {
	// List lists all SubnamespaceAnchors in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha2.SubnamespaceAnchor, err error)
	// Get retrieves the SubnamespaceAnchor from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha2.SubnamespaceAnchor, error)
	SubnamespaceAnchorNamespaceListerExpansion
}

// subnamespaceAnchorNamespaceLister implements the SubnamespaceAnchorNamespaceLister
// interface.
type subnamespaceAnchorNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all SubnamespaceAnchors in the indexer for a given namespace.
func (s subnamespaceAnchorNamespaceLister) List(selector labels.Selector) (ret []*v1alpha2.SubnamespaceAnchor, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha2.SubnamespaceAnchor))
	})
	return ret, err
}

// Get retrieves the SubnamespaceAnchor from the indexer for a given namespace and name.
func (s subnamespaceAnchorNamespaceLister) Get(name string) (*v1alpha2.SubnamespaceAnchor, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha2.Resource("subnamespaceanchor"), name)
	}
	return obj.(*v1alpha2.SubnamespaceAnchor), nil
}
