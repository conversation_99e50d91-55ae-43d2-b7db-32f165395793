#!/usr/bin/env bash
# Copyright 2017 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# run me in go.sum's folder, rootdir of project or submodule

set -o errexit
set -o nounset
set -o pipefail

echo ${BASH_SOURCE[0]}
SCRIPT_ROOT=$(dirname "${BASH_SOURCE[0]}")/..
echo $SCRIPT_ROOT
CODEGEN_VERSION=$(grep 'k8s.io/code-generator' go.sum | awk '{print $2}' | sed 's/\/go.mod//g' | head -1)
CODEGEN_PKG=$(echo $(go env GOPATH)"/pkg/mod/k8s.io/code-generator@${CODEGEN_VERSION}")
echo $CODEGEN_PKG
# CODEGEN_PKG="vendor/k8s.io/code-generator/"
# echo $CODEGEN_PKG
# generate the code with:
# --output-base    because this script should also be able to run inside the vendor dir of
#                  k8s.io/kubernetes. The output-base is needed for the generators to output into the vendor dir
#                  instead of the $GOPATH directly. For normal projects this can be dropped.
#                  default is $GOPATH
# bash "${CODEGEN_PKG}"/generate-groups.sh "deepcopy,client,informer,lister" \
bash "${CODEGEN_PKG}"/generate-groups.sh all \
  git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated \
  git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis \
  hnc.x-k8s.io:v1alpha2 \
  --go-header-file "${SCRIPT_ROOT}"/hack/boilerplate.go.txt \
  --output-base "${SCRIPT_ROOT}"
# --output-base $(pwd)/../../

# should create soft link for git-plat.tecorigin.net/ai-platform/scheduling-manager

# To use your own boilerplate text append:
#   --go-header-file "${SCRIPT_ROOT}"/hack/custom-boilerplate.go.txt
