//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/server"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, *conf.K8SConfig, *conf.Tenant,
	*conf.Harbor, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		server.ProviderSet, repository.ProviderSet, biz.ProviderSet,
		service.ProviderSet, newApp))
}
