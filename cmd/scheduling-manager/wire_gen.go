// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"git-plat.tecorigin.net/ai-platform/backend-lib/metrics"
	node2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/node"
	pool2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/pool"
	queue2 "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/tenant"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/workspace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/node"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/pool"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/secret"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/server"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, k8SConfig *conf.K8SConfig, confTenant *conf.Tenant, harbor *conf.Harbor, logger log.Logger) (*kratos.App, func(), error) {
	k8sClient, err := data.NewK8sClient(k8SConfig, logger)
	if err != nil {
		return nil, nil, err
	}
	iNamespaceRepo := namespace.NewK8sNamespaceRepo(k8sClient, logger)
	iSubNamespaceRepo := namespace.NewK8sSubNamespaceRepo(k8sClient, logger)
	ihrqRepo := quota.NewK8sHrqRepo(k8sClient, logger)
	iSecretRepo := secret.NewK8sSecretRepo(k8sClient)
	iUsecase := tenant.NewTenantUsecase(iNamespaceRepo, iSubNamespaceRepo, ihrqRepo, iSecretRepo, harbor, logger)
	iRepo := queue.NewK8sQueueRepo(k8sClient, logger)
	iQuotaRepo := quota.NewK8sQuotaRepo(k8sClient, logger)
	workspaceIUsecase := workspace.NewWorkspaceUsecase(iRepo, iQuotaRepo, ihrqRepo, iNamespaceRepo, iSubNamespaceRepo, iSecretRepo, harbor, logger)
	iRepository := node.NewK8sNodeRepo(k8sClient, logger)
	natsJsClient, cleanup, err := data.NewNatsJs(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	imqRepository := node.NewNatsRepository(natsJsClient, logger)
	iWatchRepository := cache.NewK8sWatchRepo(k8sClient, logger)
	nodeIUsecase := node2.NewNodeUsecase(iRepository, iNamespaceRepo, ihrqRepo, imqRepository, iWatchRepository, logger)
	queueIUsecase := queue2.NewQueueUsecase(iRepo, logger)
	cacheIMQRepository := cache.NewNatsRepository(natsJsClient, logger)
	watchIUsecase := watch.NewWatchUsecase(iWatchRepository, cacheIMQRepository, logger)
	rpcClient, err := data.NewRpcClient(confTenant)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	iRrpcRepository := pool.NewRPCRepository(rpcClient)
	poolIUsecase := pool2.NewUsecase(iRrpcRepository, iWatchRepository, iNamespaceRepo, ihrqRepo, logger)
	schedulingApi := service.NewSchedulingApi(iUsecase, workspaceIUsecase, nodeIUsecase, queueIUsecase, watchIUsecase, poolIUsecase)
	grpcServer := server.NewGRPCServer(confServer, schedulingApi, logger)
	httpServer := server.NewHTTPServer(confServer, schedulingApi, logger)
	metricsServer := metrics.NewMetricsServer()
	app := newApp(logger, grpcServer, httpServer, metricsServer)
	return app, func() {
		cleanup()
	}, nil
}
