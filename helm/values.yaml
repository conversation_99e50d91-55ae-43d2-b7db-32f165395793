# Default values for helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
labels:
  job_type: computility
  job_name: scheduling-manager
  branch: develop
  k8s.kuboard.cn/layer: svc
annotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "7070"
  prometheus.io/scrape: "true"
replicaCount: 1

image:
  repository: registry.tecorigin.io:5443
  project: computility
  arch: x86_64
  name: scheduling-manager
  pullPolicy: IfNotPresent
  tag: ""

command:
  - /bin/scheduling-manager

args:
  - -conf
  - /configs/config.yaml

env:

ports:
  - name: http
    port: 8000
    protocol: TCP
  - name: grpc
    port: 9000
    protocol: TCP
  - name: metric
    port: 7070
    protocol: TCP

service:
  enable: true
  # 服务类型：Headless,ClusterIP,NodePort
  type: NodePort

# 挂载卷类型包括：pvc,configmap,sercet,hostpath,emptydir，vct（statefulset的volumeClaimTemplates）
# 挂载卷名称需要和对应类型名称一致
mounts:
  - name: scheduling-manager-config
    mountPath: /configs/config.yaml
    subPath: config.yaml
    type: configmap

# 仅当挂载卷类型为pvc或vct需要填写
persistentVolume:
  enable: false

readinessProbe:

livenessProbe:

resources:

securityContext:

tolerations:

nodeSelector:
  kubernetes.io/action: platform

affinity:

# configmap
schedulingManager:
  configmap:
    server:
      http:
        addr: 0.0.0.0:8000
        timeout: 120s
      grpc:
        addr: 0.0.0.0:9000
        timeout: 120s
    data:
      database:
        driver: mysql
        source: root:root@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
      nats:
        url: nats://nats.nats:4222
      redis:
        addr: 127.0.0.1:6379
        read_timeout: 0.2s
        write_timeout: 0.2s
    k8sConfig:
      outCluster: false
      devEnv: false
      devConfig:
        host: https://*********:6443
        #    host: https://127.0.0.1:6443
        #        host: https://*********:6443
        # ********* admin-user-token-s7zvw
        bearerToken: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        # env-test
        # bearerToken: ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      QPS: 100
      Burst: 100
    tenant:
      server:
        http:
          addr: tenant.computility:8000
          timeout: 120s
        grpc:
          addr: tenant.computility:9000
          timeout: 120s
    harbor:
      url: https://business.tecorigin.io:5443
      username: admin
      password: Teco@135
      secretName: harbor-image-pull-secret
      insecureRegistry: true
