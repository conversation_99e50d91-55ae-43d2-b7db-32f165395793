apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}
data:
  config.yaml: |
    server:
      http:
        addr: {{ .Values.schedulingManager.configmap.server.http.addr }}
        timeout: {{ .Values.schedulingManager.configmap.server.http.timeout }}
      grpc:
        addr: {{ .Values.schedulingManager.configmap.server.grpc.addr }}
        timeout: {{ .Values.schedulingManager.configmap.server.grpc.timeout }}
    data:
      database:
        driver: {{ .Values.schedulingManager.configmap.data.database.driver }}
        source: {{ .Values.schedulingManager.configmap.data.database.source }}
      nats:
        url: {{ .Values.schedulingManager.configmap.data.nats.url }}
      redis:
        addr: {{ .Values.schedulingManager.configmap.data.redis.addr }}
        read_timeout: {{ .Values.schedulingManager.configmap.data.redis.read_timeout }}
        write_timeout: {{ .Values.schedulingManager.configmap.data.redis.write_timeout }}
    k8sConfig:
      outCluster: {{ .Values.schedulingManager.configmap.k8sConfig.outCluster }}
      devEnv: {{ .Values.schedulingManager.configmap.k8sConfig.devEnv }}
      devConfig:
        host: {{ .Values.schedulingManager.configmap.k8sConfig.devConfig.host }}
        bearerToken: {{ .Values.schedulingManager.configmap.k8sConfig.devConfig.bearerToken }}
      QPS: {{ .Values.schedulingManager.configmap.k8sConfig.QPS }}
      Burst: {{ .Values.schedulingManager.configmap.k8sConfig.Burst }}
    tenant:
      server:
        http:
          addr: {{ .Values.schedulingManager.configmap.tenant.server.http.addr }}
          timeout: {{ .Values.schedulingManager.configmap.tenant.server.http.timeout }}
        grpc:
          addr: {{ .Values.schedulingManager.configmap.tenant.server.grpc.addr }}
          timeout: {{ .Values.schedulingManager.configmap.tenant.server.grpc.timeout }}
    harbor:
      url: {{ .Values.schedulingManager.configmap.harbor.url }}
      username: {{ .Values.schedulingManager.configmap.harbor.username }}
      password: {{ .Values.schedulingManager.configmap.harbor.password }}
      secretName: {{ .Values.schedulingManager.configmap.harbor.secretName }}
      insecureRegistry: {{ .Values.schedulingManager.configmap.harbor.insecureRegistry }}
