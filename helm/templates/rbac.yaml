---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Chart.Name }}
  namespace: computility

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Chart.Name }}
rules:
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - create
      - get
      - list
      - watch
      - delete
  - apiGroups:
      - batch.volcano.sh
    resources:
      - jobs
    verbs:
      - get
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - batch.volcano.sh
    resources:
      - jobs/status
    verbs:
      - update
      - patch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/status
    verbs:
      - create
      - get
      - list
      - watch
      - update
      - patch
      - bind
      - updateStatus
      - delete
  - apiGroups:
      - ""
    resources:
      - pods/binding
    verbs:
      - create
  - apiGroups:
      - ""
    resources:
      - persistentvolumeclaims
    verbs:
      - list
      - watch
      - update
  - apiGroups:
      - ""
    resources:
      - persistentvolumes
    verbs:
      - list
      - watch
      - update
  - apiGroups:
      - ""
    resources:
      - namespaces
      - services
      - replicationcontrollers
    verbs:
      - create
      - delete
      - update
      - patch
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - resourcequotas
    verbs:
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - nodes
    verbs:
      - create
      - delete
      - get
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
      - csinodes
      - csidrivers
      - csistoragecapacities
    verbs:
      - list
      - watch
  - apiGroups:
      - policy
    resources:
      - poddisruptionbudgets
    verbs:
      - list
      - watch
  - apiGroups:
      - scheduling.k8s.io
    resources:
      - priorityclasses
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - scheduling.incubator.k8s.io
      - scheduling.volcano.sh
    resources:
      - queues
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - update
      - patch
  - apiGroups:
      - scheduling.incubator.k8s.io
      - scheduling.volcano.sh
    resources:
      - queues/status
    verbs:
      - update
  - apiGroups:
      - scheduling.incubator.k8s.io
      - scheduling.volcano.sh
    resources:
      - podgroups
    verbs:
      - list
      - watch
      - update
  - apiGroups:
      - nodeinfo.volcano.sh
    resources:
      - numatopologies
    verbs:
      - get
      - list
      - watch
      - delete
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - create
      - delete
      - update
  - apiGroups:
      - apps
    resources:
      - daemonsets
      - replicasets
      - statefulsets
    verbs:
      - list
      - watch
      - get
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - create
      - update
      - watch
  - apiGroups:
      - hnc.x-k8s.io
    resources:
      - hncconfigurations
      - hierarchyconfigurations
      - hierarchicalresourcequotas
      - subnamespaceanchors
    verbs:
      - create
      - delete
      - update
      - patch
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - create
      - get
      - list
      - watch
      - update
      - delete
      - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Chart.Name }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Chart.Name }}
subjects:
  - kind: ServiceAccount
    name: {{ .Chart.Name }}
    namespace: computility
