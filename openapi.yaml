# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: SchedulingApi API
    description: The scheduling api service definition.
    version: 0.0.1
paths:
    /v1/scheduling-manager/node:
        post:
            tags:
                - SchedulingApi
            description: nodes
            operationId: SchedulingApi_LableNode
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LabelNodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_UnlabelNode
            parameters:
                - name: nodeName
                  in: query
                  schema:
                    type: string
                - name: labelKey
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/node/pool:
        post:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_AllocateNodeToResourcePool
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AllocateNodeToResourcePoolRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_DeallocateNodeToResourcePool
            parameters:
                - name: nodeName
                  in: query
                  schema:
                    type: string
                - name: pool
                  in: query
                  schema:
                    type: integer
                    format: enum
                - name: poolId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/node/tenant:
        post:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_AllocateNodeToTenant
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AllocateNodeToTenantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_DeallocateNodeToTenant
            parameters:
                - name: nodeName
                  in: query
                  schema:
                    type: string
                - name: tenantId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/node/workspace:
        post:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_AllocateNodeToWorkspace
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AllocateNodeToWorkspaceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_DeallocateNodeToWorkspace
            parameters:
                - name: nodeName
                  in: query
                  schema:
                    type: string
                - name: workspaceId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/pod:
        post:
            tags:
                - SchedulingApi
            description: pod & pods
            operationId: SchedulingApi_CreatePod
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreatePodRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreatePodReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/pod/count:
        post:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_QueryPodCount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreatePodRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryPodCountReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/pods:
        post:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_CreatePods
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreatePodsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreatePodsReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/pool/resource/available:
        post:
            tags:
                - SchedulingApi
            description: resource pool
            operationId: SchedulingApi_ResourceAvailable
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ResourceAvailableRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ResourceAvailableReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/tenant:
        put:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_SyncTenant
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateTenantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateTenantReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - SchedulingApi
            description: tenants
            operationId: SchedulingApi_CreateTenant
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateTenantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateTenantReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/tenant/{tenantId}:
        delete:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_RemoveTenant
            parameters:
                - name: tenantId
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_UpdateTenant
            parameters:
                - name: tenantId
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateTenantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/workspace:
        put:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_SyncWorkspace
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateWorkspaceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateWorkspaceReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - SchedulingApi
            description: workspace
            operationId: SchedulingApi_CreateWorkspace
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateWorkspaceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateWorkspaceReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/scheduling-manager/workspace/{workspaceId}:
        delete:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_RemoveWorkspace
            parameters:
                - name: workspaceId
                  in: path
                  required: true
                  schema:
                    type: string
                - name: tenantId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - SchedulingApi
            operationId: SchedulingApi_UpdateWorkspace
            parameters:
                - name: workspaceId
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateWorkspaceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AllocateNodeToResourcePoolRequest:
            type: object
            properties:
                nodeName:
                    type: string
                pool:
                    type: integer
                    format: enum
                poolId:
                    type: string
        AllocateNodeToTenantRequest:
            type: object
            properties:
                nodeName:
                    type: string
                tenantId:
                    type: string
        AllocateNodeToWorkspaceRequest:
            type: object
            properties:
                nodeName:
                    type: string
                workspaceId:
                    type: string
        Bucket:
            type: object
            properties:
                id:
                    type: string
                path:
                    type: string
        BucketMount:
            type: object
            properties:
                id:
                    type: string
                mntPath:
                    type: string
                readOnly:
                    type: boolean
        Configuration:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                unit:
                    type: array
                    items:
                        type: string
                type:
                    type: integer
                    format: enum
                category:
                    type: string
                productForm:
                    type: array
                    items:
                        $ref: '#/components/schemas/Form'
                capacity:
                    type: string
        Container:
            type: object
            properties:
                name:
                    type: string
                image:
                    type: string
                command:
                    type: string
                resource:
                    $ref: '#/components/schemas/ResourceClaim'
                buckets:
                    type: array
                    items:
                        $ref: '#/components/schemas/BucketMount'
        Cpu:
            type: object
            properties:
                num:
                    type: string
                architecture:
                    type: string
        CreatePodReply:
            type: object
            properties:
                podId:
                    type: string
        CreatePodRequest:
            type: object
            properties:
                name:
                    type: string
                tenantId:
                    type: string
                workspaceId:
                    type: string
                projectId:
                    type: string
                restartPolicy:
                    type: integer
                    format: enum
                containers:
                    type: array
                    items:
                        $ref: '#/components/schemas/Container'
                buckets:
                    type: array
                    items:
                        $ref: '#/components/schemas/Bucket'
                billingMode:
                    type: integer
                    format: enum
                merchandise:
                    type: string
        CreatePodsReply:
            type: object
            properties:
                podIdMap:
                    type: object
                    additionalProperties:
                        type: string
                    description: name -> pod_id
        CreatePodsRequest:
            type: object
            properties:
                requests:
                    type: array
                    items:
                        $ref: '#/components/schemas/CreatePodRequest'
        CreateTenantReply:
            type: object
            properties:
                namespaces:
                    type: object
                    additionalProperties:
                        type: string
                    description: |-
                        resource pool -> namespace
                         key is the integer value of ResourcePool.Type
        CreateTenantRequest:
            type: object
            properties:
                name:
                    type: string
                tenantId:
                    type: string
                quotas:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/ResourceClaim'
                    description: |-
                        resource pool -> quota
                         pool: integer value of ResourcePool.Type
        CreateWorkspaceReply:
            type: object
            properties:
                namespaces:
                    type: object
                    additionalProperties:
                        type: string
                    description: |-
                        resource pool -> namespace
                         key is the integer value of ResourcePool.Type
        CreateWorkspaceRequest:
            type: object
            properties:
                name:
                    type: string
                tenantId:
                    type: string
                workspaceId:
                    type: string
                quotas:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/ResourceClaim'
                    description: |-
                        resource pool -> quota
                         key is the integer value of ResourcePool.Type
        Form:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                form:
                    type: string
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        LabelNodeRequest:
            type: object
            properties:
                nodeName:
                    type: string
                labelKey:
                    type: string
                labelValue:
                    type: string
        QueryPodCountReply:
            type: object
            properties:
                count:
                    type: string
        ResourceAvailableReply:
            type: object
            properties:
                available:
                    type: string
                reason:
                    type: string
                keyResources:
                    type: array
                    items:
                        type: string
                detail:
                    type: string
        ResourceAvailableRequest:
            type: object
            properties:
                pool:
                    type: integer
                    format: enum
                specId:
                    type: string
                configurations:
                    type: array
                    items:
                        $ref: '#/components/schemas/SpecConfiguration'
        ResourceClaim:
            type: object
            properties:
                cpu:
                    $ref: '#/components/schemas/Cpu'
                memory:
                    type: string
                extendedResource:
                    type: object
                    additionalProperties:
                        type: string
                    description: key is the int value of ExtendedResource.Type
        SpecConfiguration:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                type:
                    type: integer
                    format: enum
                unit:
                    type: string
                enable:
                    type: boolean
                valueType:
                    type: integer
                    format: enum
                value:
                    type: string
                configuration:
                    $ref: '#/components/schemas/Configuration'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: SchedulingApi
