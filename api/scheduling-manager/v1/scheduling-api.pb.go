// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.12.4
// source: api/scheduling-manager/v1/scheduling-api.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RestartPolicy_Type int32

const (
	RestartPolicy_UNKNOWN    RestartPolicy_Type = 0
	RestartPolicy_ALWAYS     RestartPolicy_Type = 1
	RestartPolicy_ON_FAILURE RestartPolicy_Type = 2
	RestartPolicy_NEVER      RestartPolicy_Type = 3
)

// Enum value maps for RestartPolicy_Type.
var (
	RestartPolicy_Type_name = map[int32]string{
		0: "UNKNOWN",
		1: "ALWAYS",
		2: "ON_FAILURE",
		3: "NEVER",
	}
	RestartPolicy_Type_value = map[string]int32{
		"UNKNOWN":    0,
		"ALWAYS":     1,
		"ON_FAILURE": 2,
		"NEVER":      3,
	}
)

func (x RestartPolicy_Type) Enum() *RestartPolicy_Type {
	p := new(RestartPolicy_Type)
	*p = x
	return p
}

func (x RestartPolicy_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RestartPolicy_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes[0].Descriptor()
}

func (RestartPolicy_Type) Type() protoreflect.EnumType {
	return &file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes[0]
}

func (x RestartPolicy_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RestartPolicy_Type.Descriptor instead.
func (RestartPolicy_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{0, 0}
}

type ExtendedResource_Type int32

const (
	ExtendedResource_UNKNOWN  ExtendedResource_Type = 0
	ExtendedResource_GPU      ExtendedResource_Type = 1
	ExtendedResource_TECO     ExtendedResource_Type = 2
	ExtendedResource_TECOCORE ExtendedResource_Type = 3
	ExtendedResource_DCU      ExtendedResource_Type = 4
)

// Enum value maps for ExtendedResource_Type.
var (
	ExtendedResource_Type_name = map[int32]string{
		0: "UNKNOWN",
		1: "GPU",
		2: "TECO",
		3: "TECOCORE",
		4: "DCU",
	}
	ExtendedResource_Type_value = map[string]int32{
		"UNKNOWN":  0,
		"GPU":      1,
		"TECO":     2,
		"TECOCORE": 3,
		"DCU":      4,
	}
)

func (x ExtendedResource_Type) Enum() *ExtendedResource_Type {
	p := new(ExtendedResource_Type)
	*p = x
	return p
}

func (x ExtendedResource_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExtendedResource_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes[1].Descriptor()
}

func (ExtendedResource_Type) Type() protoreflect.EnumType {
	return &file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes[1]
}

func (x ExtendedResource_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExtendedResource_Type.Descriptor instead.
func (ExtendedResource_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{1, 0}
}

type ResourcePool_Type int32

const (
	ResourcePool_Unknown   ResourcePool_Type = 0
	ResourcePool_Reserved  ResourcePool_Type = 1
	ResourcePool_Dedicated ResourcePool_Type = 2
	ResourcePool_Shared    ResourcePool_Type = 3
)

// Enum value maps for ResourcePool_Type.
var (
	ResourcePool_Type_name = map[int32]string{
		0: "Unknown",
		1: "Reserved",
		2: "Dedicated",
		3: "Shared",
	}
	ResourcePool_Type_value = map[string]int32{
		"Unknown":   0,
		"Reserved":  1,
		"Dedicated": 2,
		"Shared":    3,
	}
)

func (x ResourcePool_Type) Enum() *ResourcePool_Type {
	p := new(ResourcePool_Type)
	*p = x
	return p
}

func (x ResourcePool_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourcePool_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes[2].Descriptor()
}

func (ResourcePool_Type) Type() protoreflect.EnumType {
	return &file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes[2]
}

func (x ResourcePool_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourcePool_Type.Descriptor instead.
func (ResourcePool_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{12, 0}
}

type RestartPolicy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartPolicy) Reset() {
	*x = RestartPolicy{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartPolicy) ProtoMessage() {}

func (x *RestartPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartPolicy.ProtoReflect.Descriptor instead.
func (*RestartPolicy) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{0}
}

type ExtendedResource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtendedResource) Reset() {
	*x = ExtendedResource{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtendedResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendedResource) ProtoMessage() {}

func (x *ExtendedResource) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendedResource.ProtoReflect.Descriptor instead.
func (*ExtendedResource) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{1}
}

type Cpu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Num           int64                  `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"` // 1000, one core.
	Architecture  string                 `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Cpu) Reset() {
	*x = Cpu{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Cpu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cpu) ProtoMessage() {}

func (x *Cpu) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cpu.ProtoReflect.Descriptor instead.
func (*Cpu) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{2}
}

func (x *Cpu) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Cpu) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

type ResourceClaim struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Cpu    *Cpu                   `protobuf:"bytes,1,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory uint64                 `protobuf:"fixed64,2,opt,name=memory,proto3" json:"memory,omitempty"` // bytes
	// key is the int value of ExtendedResource.Type
	ExtendedResource map[int32]uint64 `protobuf:"bytes,3,rep,name=extended_resource,json=extendedResource,proto3" json:"extended_resource,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ResourceClaim) Reset() {
	*x = ResourceClaim{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceClaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceClaim) ProtoMessage() {}

func (x *ResourceClaim) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceClaim.ProtoReflect.Descriptor instead.
func (*ResourceClaim) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{3}
}

func (x *ResourceClaim) GetCpu() *Cpu {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *ResourceClaim) GetMemory() uint64 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *ResourceClaim) GetExtendedResource() map[int32]uint64 {
	if x != nil {
		return x.ExtendedResource
	}
	return nil
}

type Bucket struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Path          string                 `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bucket) Reset() {
	*x = Bucket{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bucket) ProtoMessage() {}

func (x *Bucket) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bucket.ProtoReflect.Descriptor instead.
func (*Bucket) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{4}
}

func (x *Bucket) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Bucket) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type BucketMount struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	MntPath       string                 `protobuf:"bytes,2,opt,name=mnt_path,json=mntPath,proto3" json:"mnt_path,omitempty"`
	ReadOnly      bool                   `protobuf:"varint,3,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BucketMount) Reset() {
	*x = BucketMount{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BucketMount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BucketMount) ProtoMessage() {}

func (x *BucketMount) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BucketMount.ProtoReflect.Descriptor instead.
func (*BucketMount) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{5}
}

func (x *BucketMount) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BucketMount) GetMntPath() string {
	if x != nil {
		return x.MntPath
	}
	return ""
}

func (x *BucketMount) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

type Container struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`
	Resource      *ResourceClaim         `protobuf:"bytes,4,opt,name=resource,proto3" json:"resource,omitempty"`
	Buckets       []*BucketMount         `protobuf:"bytes,5,rep,name=buckets,proto3" json:"buckets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Container) Reset() {
	*x = Container{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Container) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Container) ProtoMessage() {}

func (x *Container) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Container.ProtoReflect.Descriptor instead.
func (*Container) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{6}
}

func (x *Container) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Container) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Container) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *Container) GetResource() *ResourceClaim {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *Container) GetBuckets() []*BucketMount {
	if x != nil {
		return x.Buckets
	}
	return nil
}

type CreatePodRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TenantId      string                 `protobuf:"bytes,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	WorkspaceId   string                 `protobuf:"bytes,3,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	ProjectId     string                 `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	RestartPolicy RestartPolicy_Type     `protobuf:"varint,5,opt,name=restart_policy,json=restartPolicy,proto3,enum=api.scheduling_manager.v1.RestartPolicy_Type" json:"restart_policy,omitempty"`
	Containers    []*Container           `protobuf:"bytes,6,rep,name=containers,proto3" json:"containers,omitempty"`
	Buckets       []*Bucket              `protobuf:"bytes,7,rep,name=buckets,proto3" json:"buckets,omitempty"`
	BillingMode   BilingMode_Type        `protobuf:"varint,8,opt,name=billing_mode,json=billingMode,proto3,enum=api.scheduling_manager.v1.BilingMode_Type" json:"billing_mode,omitempty"`
	Merchandise   string                 `protobuf:"bytes,9,opt,name=merchandise,proto3" json:"merchandise,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePodRequest) Reset() {
	*x = CreatePodRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePodRequest) ProtoMessage() {}

func (x *CreatePodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePodRequest.ProtoReflect.Descriptor instead.
func (*CreatePodRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{7}
}

func (x *CreatePodRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePodRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *CreatePodRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *CreatePodRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CreatePodRequest) GetRestartPolicy() RestartPolicy_Type {
	if x != nil {
		return x.RestartPolicy
	}
	return RestartPolicy_UNKNOWN
}

func (x *CreatePodRequest) GetContainers() []*Container {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *CreatePodRequest) GetBuckets() []*Bucket {
	if x != nil {
		return x.Buckets
	}
	return nil
}

func (x *CreatePodRequest) GetBillingMode() BilingMode_Type {
	if x != nil {
		return x.BillingMode
	}
	return BilingMode_UNKNOWN
}

func (x *CreatePodRequest) GetMerchandise() string {
	if x != nil {
		return x.Merchandise
	}
	return ""
}

type CreatePodReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PodId         string                 `protobuf:"bytes,1,opt,name=pod_id,json=podId,proto3" json:"pod_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePodReply) Reset() {
	*x = CreatePodReply{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePodReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePodReply) ProtoMessage() {}

func (x *CreatePodReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePodReply.ProtoReflect.Descriptor instead.
func (*CreatePodReply) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{8}
}

func (x *CreatePodReply) GetPodId() string {
	if x != nil {
		return x.PodId
	}
	return ""
}

type CreatePodsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Requests      []*CreatePodRequest    `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePodsRequest) Reset() {
	*x = CreatePodsRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePodsRequest) ProtoMessage() {}

func (x *CreatePodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePodsRequest.ProtoReflect.Descriptor instead.
func (*CreatePodsRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{9}
}

func (x *CreatePodsRequest) GetRequests() []*CreatePodRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

type CreatePodsReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name -> pod_id
	PodIdMap      map[string]string `protobuf:"bytes,1,rep,name=pod_id_map,json=podIdMap,proto3" json:"pod_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePodsReply) Reset() {
	*x = CreatePodsReply{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePodsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePodsReply) ProtoMessage() {}

func (x *CreatePodsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePodsReply.ProtoReflect.Descriptor instead.
func (*CreatePodsReply) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{10}
}

func (x *CreatePodsReply) GetPodIdMap() map[string]string {
	if x != nil {
		return x.PodIdMap
	}
	return nil
}

type QueryPodCountReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         uint64                 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryPodCountReply) Reset() {
	*x = QueryPodCountReply{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryPodCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPodCountReply) ProtoMessage() {}

func (x *QueryPodCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPodCountReply.ProtoReflect.Descriptor instead.
func (*QueryPodCountReply) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{11}
}

func (x *QueryPodCountReply) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ResourcePool struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourcePool) Reset() {
	*x = ResourcePool{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourcePool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePool) ProtoMessage() {}

func (x *ResourcePool) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePool.ProtoReflect.Descriptor instead.
func (*ResourcePool) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{12}
}

type CreateTenantRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Name     string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TenantId string                 `protobuf:"bytes,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// resource pool -> quota
	// pool: integer value of ResourcePool.Type
	Quotas        map[int32]*ResourceClaim `protobuf:"bytes,4,rep,name=quotas,proto3" json:"quotas,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTenantRequest) Reset() {
	*x = CreateTenantRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTenantRequest) ProtoMessage() {}

func (x *CreateTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTenantRequest.ProtoReflect.Descriptor instead.
func (*CreateTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{13}
}

func (x *CreateTenantRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTenantRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *CreateTenantRequest) GetQuotas() map[int32]*ResourceClaim {
	if x != nil {
		return x.Quotas
	}
	return nil
}

type CreateTenantReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// resource pool -> namespace
	// key is the integer value of ResourcePool.Type
	Namespaces    map[int32]string `protobuf:"bytes,1,rep,name=namespaces,proto3" json:"namespaces,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTenantReply) Reset() {
	*x = CreateTenantReply{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTenantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTenantReply) ProtoMessage() {}

func (x *CreateTenantReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTenantReply.ProtoReflect.Descriptor instead.
func (*CreateTenantReply) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{14}
}

func (x *CreateTenantReply) GetNamespaces() map[int32]string {
	if x != nil {
		return x.Namespaces
	}
	return nil
}

type RemoveTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveTenantRequest) Reset() {
	*x = RemoveTenantRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveTenantRequest) ProtoMessage() {}

func (x *RemoveTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveTenantRequest.ProtoReflect.Descriptor instead.
func (*RemoveTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{15}
}

func (x *RemoveTenantRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

type CreateWorkspaceRequest struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	Name        string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TenantId    string                 `protobuf:"bytes,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	WorkspaceId string                 `protobuf:"bytes,3,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	// resource pool -> quota
	// key is the integer value of ResourcePool.Type
	Quotas        map[int32]*ResourceClaim `protobuf:"bytes,5,rep,name=quotas,proto3" json:"quotas,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWorkspaceRequest) Reset() {
	*x = CreateWorkspaceRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkspaceRequest) ProtoMessage() {}

func (x *CreateWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{16}
}

func (x *CreateWorkspaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetQuotas() map[int32]*ResourceClaim {
	if x != nil {
		return x.Quotas
	}
	return nil
}

type CreateWorkspaceReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// resource pool -> namespace
	// key is the integer value of ResourcePool.Type
	Namespaces    map[int32]string `protobuf:"bytes,1,rep,name=namespaces,proto3" json:"namespaces,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWorkspaceReply) Reset() {
	*x = CreateWorkspaceReply{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkspaceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkspaceReply) ProtoMessage() {}

func (x *CreateWorkspaceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkspaceReply.ProtoReflect.Descriptor instead.
func (*CreateWorkspaceReply) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{17}
}

func (x *CreateWorkspaceReply) GetNamespaces() map[int32]string {
	if x != nil {
		return x.Namespaces
	}
	return nil
}

type RemoveWorkspaceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	WorkspaceId   string                 `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveWorkspaceRequest) Reset() {
	*x = RemoveWorkspaceRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveWorkspaceRequest) ProtoMessage() {}

func (x *RemoveWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*RemoveWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{18}
}

func (x *RemoveWorkspaceRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *RemoveWorkspaceRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

type LabelNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeName      string                 `protobuf:"bytes,1,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`
	LabelKey      string                 `protobuf:"bytes,2,opt,name=label_key,json=labelKey,proto3" json:"label_key,omitempty"`
	LabelValue    string                 `protobuf:"bytes,3,opt,name=label_value,json=labelValue,proto3" json:"label_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelNodeRequest) Reset() {
	*x = LabelNodeRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelNodeRequest) ProtoMessage() {}

func (x *LabelNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelNodeRequest.ProtoReflect.Descriptor instead.
func (*LabelNodeRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{19}
}

func (x *LabelNodeRequest) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *LabelNodeRequest) GetLabelKey() string {
	if x != nil {
		return x.LabelKey
	}
	return ""
}

func (x *LabelNodeRequest) GetLabelValue() string {
	if x != nil {
		return x.LabelValue
	}
	return ""
}

type UnlabelNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeName      string                 `protobuf:"bytes,1,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`
	LabelKey      string                 `protobuf:"bytes,2,opt,name=label_key,json=labelKey,proto3" json:"label_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnlabelNodeRequest) Reset() {
	*x = UnlabelNodeRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnlabelNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlabelNodeRequest) ProtoMessage() {}

func (x *UnlabelNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlabelNodeRequest.ProtoReflect.Descriptor instead.
func (*UnlabelNodeRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{20}
}

func (x *UnlabelNodeRequest) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *UnlabelNodeRequest) GetLabelKey() string {
	if x != nil {
		return x.LabelKey
	}
	return ""
}

type AllocateNodeToTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeName      string                 `protobuf:"bytes,1,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`
	TenantId      string                 `protobuf:"bytes,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AllocateNodeToTenantRequest) Reset() {
	*x = AllocateNodeToTenantRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AllocateNodeToTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocateNodeToTenantRequest) ProtoMessage() {}

func (x *AllocateNodeToTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocateNodeToTenantRequest.ProtoReflect.Descriptor instead.
func (*AllocateNodeToTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{21}
}

func (x *AllocateNodeToTenantRequest) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *AllocateNodeToTenantRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

type AllocateNodeToWorkspaceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeName      string                 `protobuf:"bytes,1,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`
	WorkspaceId   string                 `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AllocateNodeToWorkspaceRequest) Reset() {
	*x = AllocateNodeToWorkspaceRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AllocateNodeToWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocateNodeToWorkspaceRequest) ProtoMessage() {}

func (x *AllocateNodeToWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocateNodeToWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*AllocateNodeToWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{22}
}

func (x *AllocateNodeToWorkspaceRequest) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *AllocateNodeToWorkspaceRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

type AllocateNodeToResourcePoolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeName      string                 `protobuf:"bytes,1,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`
	Pool          ResourcePool_Type      `protobuf:"varint,2,opt,name=pool,proto3,enum=api.scheduling_manager.v1.ResourcePool_Type" json:"pool,omitempty"`
	PoolId        string                 `protobuf:"bytes,3,opt,name=pool_id,json=poolId,proto3" json:"pool_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AllocateNodeToResourcePoolRequest) Reset() {
	*x = AllocateNodeToResourcePoolRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AllocateNodeToResourcePoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocateNodeToResourcePoolRequest) ProtoMessage() {}

func (x *AllocateNodeToResourcePoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocateNodeToResourcePoolRequest.ProtoReflect.Descriptor instead.
func (*AllocateNodeToResourcePoolRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{23}
}

func (x *AllocateNodeToResourcePoolRequest) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *AllocateNodeToResourcePoolRequest) GetPool() ResourcePool_Type {
	if x != nil {
		return x.Pool
	}
	return ResourcePool_Unknown
}

func (x *AllocateNodeToResourcePoolRequest) GetPoolId() string {
	if x != nil {
		return x.PoolId
	}
	return ""
}

type ResourceAvailableRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Pool           ResourcePool_Type      `protobuf:"varint,1,opt,name=pool,proto3,enum=api.scheduling_manager.v1.ResourcePool_Type" json:"pool,omitempty"`
	SpecId         string                 `protobuf:"bytes,2,opt,name=spec_id,json=specId,proto3" json:"spec_id,omitempty"`
	Configurations []*SpecConfiguration   `protobuf:"bytes,3,rep,name=configurations,proto3" json:"configurations,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ResourceAvailableRequest) Reset() {
	*x = ResourceAvailableRequest{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceAvailableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceAvailableRequest) ProtoMessage() {}

func (x *ResourceAvailableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceAvailableRequest.ProtoReflect.Descriptor instead.
func (*ResourceAvailableRequest) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{24}
}

func (x *ResourceAvailableRequest) GetPool() ResourcePool_Type {
	if x != nil {
		return x.Pool
	}
	return ResourcePool_Unknown
}

func (x *ResourceAvailableRequest) GetSpecId() string {
	if x != nil {
		return x.SpecId
	}
	return ""
}

func (x *ResourceAvailableRequest) GetConfigurations() []*SpecConfiguration {
	if x != nil {
		return x.Configurations
	}
	return nil
}

type ResourceAvailableReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Available     int64                  `protobuf:"varint,1,opt,name=available,proto3" json:"available,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`                                 // quota, pool
	KeyResources  []string               `protobuf:"bytes,3,rep,name=key_resources,json=keyResources,proto3" json:"key_resources,omitempty"` // cpu/memory/teco/nvidia
	Detail        string                 `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceAvailableReply) Reset() {
	*x = ResourceAvailableReply{}
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceAvailableReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceAvailableReply) ProtoMessage() {}

func (x *ResourceAvailableReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceAvailableReply.ProtoReflect.Descriptor instead.
func (*ResourceAvailableReply) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP(), []int{25}
}

func (x *ResourceAvailableReply) GetAvailable() int64 {
	if x != nil {
		return x.Available
	}
	return 0
}

func (x *ResourceAvailableReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ResourceAvailableReply) GetKeyResources() []string {
	if x != nil {
		return x.KeyResources
	}
	return nil
}

func (x *ResourceAvailableReply) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

var File_api_scheduling_manager_v1_scheduling_api_proto protoreflect.FileDescriptor

var file_api_scheduling_manager_v1_scheduling_api_proto_rawDesc = string([]byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x19, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x27, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4b, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x3a, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x41, 0x4c, 0x57, 0x41, 0x59, 0x53, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4e, 0x45,
	0x56, 0x45, 0x52, 0x10, 0x03, 0x22, 0x51, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x3d, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x47, 0x50, 0x55, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x43, 0x4f, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x43, 0x4f, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x03, 0x12,
	0x07, 0x0a, 0x03, 0x44, 0x43, 0x55, 0x10, 0x04, 0x22, 0x3b, 0x0a, 0x03, 0x43, 0x70, 0x75, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75,
	0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x22, 0x9b, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x30, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x70, 0x75, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x12, 0x7b, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x9a, 0x01, 0x08, 0x22, 0x06, 0x1a, 0x04, 0x10, 0x05, 0x20, 0x00, 0x52, 0x10, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a, 0x43,
	0x0a, 0x15, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x2c, 0x0a, 0x06, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x22, 0x55, 0x0a, 0x0b, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x22, 0xd7, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x44, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x40, 0x0a, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x22, 0xcf, 0x03, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x0e, 0x72, 0x65,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x12, 0x44, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x12, 0x4d, 0x0a, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x64, 0x69, 0x73, 0x65, 0x22, 0x27, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x6f, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x64, 0x49, 0x64, 0x22, 0x5c, 0x0a,
	0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x47, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x0f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x56, 0x0a, 0x0a, 0x70, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x50, 0x6f, 0x64, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70,
	0x6f, 0x64, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x1a, 0x3b, 0x0a, 0x0d, 0x50, 0x6f, 0x64, 0x49, 0x64,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x2a, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x4c, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f, 0x6c,
	0x22, 0x3c, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x64, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x10, 0x03, 0x22, 0xa1,
	0x02, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x06, 0x71, 0x75, 0x6f,
	0x74, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x9a, 0x01, 0x08, 0x22, 0x06, 0x1a,
	0x04, 0x10, 0x04, 0x20, 0x00, 0x52, 0x06, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x1a, 0x63, 0x0a,
	0x0b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xb0, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x5c, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3b, 0x0a, 0x13, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0xd3, 0x02, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x65, 0x0a, 0x06,
	0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x9a, 0x01, 0x08, 0x22, 0x06, 0x1a, 0x04, 0x10, 0x04, 0x20, 0x00, 0x52, 0x06, 0x71, 0x75, 0x6f,
	0x74, 0x61, 0x73, 0x1a, 0x63, 0x0a, 0x0b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb6, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x5f, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x6a, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6d, 0x0a,
	0x10, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4e, 0x0a, 0x12,
	0x55, 0x6e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4b, 0x65, 0x79, 0x22, 0x69, 0x0a, 0x1b,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x1e, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x21,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6e,
	0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x04, 0x70, 0x6f, 0x6f, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x20, 0x00, 0x20, 0x01, 0x52,
	0x04, 0x70, 0x6f, 0x6f, 0x6c, 0x12, 0x20, 0x0a, 0x07, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x70, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x22, 0xe0, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x04, 0x70, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x18, 0x02, 0x18, 0x03, 0x52, 0x04, 0x70, 0x6f,
	0x6f, 0x6c, 0x12, 0x20, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x70,
	0x65, 0x63, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x16, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6b,
	0x65, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x6b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x32, 0xef, 0x17, 0x0a, 0x0d, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x69, 0x12, 0x8a, 0x01, 0x0a, 0x09, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x70, 0x6f, 0x64, 0x12, 0x8e, 0x01, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x6f, 0x64, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x70, 0x6f, 0x64, 0x73, 0x12, 0x98, 0x01, 0x0a, 0x0d, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x50, 0x6f, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01,
	0x2a, 0x22, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x6f, 0x64, 0x2f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x89, 0x01, 0x0a,
	0x0c, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x2a, 0x29, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2f, 0x7b, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x8c, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x32, 0x29, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2f, 0x7b, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x94, 0x01, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x1a,
	0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0xa2,
	0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01,
	0x2a, 0x22, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x2a, 0x2f, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x98, 0x01, 0x0a, 0x0f,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x34, 0x3a, 0x01, 0x2a, 0x32, 0x2f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0xa0, 0x01, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x1a, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x78, 0x0a, 0x09, 0x4c, 0x61, 0x62,
	0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6e,
	0x6f, 0x64, 0x65, 0x12, 0x79, 0x0a, 0x0b, 0x55, 0x6e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x6e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x2a, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x95,
	0x01, 0x0a, 0x14, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54,
	0x6f, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x54, 0x6f, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a,
	0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x61, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x54, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x12, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x2a, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x9e, 0x01,
	0x0a, 0x17, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x30, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f,
	0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x9d,
	0x01, 0x0a, 0x19, 0x44, 0x65, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x54, 0x6f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x39, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x2a, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f,
	0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x9f,
	0x01, 0x0a, 0x1a, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54,
	0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x3c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x70, 0x6f, 0x6f, 0x6c,
	0x12, 0x9e, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x12, 0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x2a,
	0x20, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x70, 0x6f, 0x6f,
	0x6c, 0x12, 0xb6, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x39, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x3a, 0x01, 0x2a, 0x22, 0x2e, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2f, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x60, 0x0a, 0x24, 0x64, 0x65,
	0x76, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x42, 0x18, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x1c,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_api_scheduling_manager_v1_scheduling_api_proto_rawDescOnce sync.Once
	file_api_scheduling_manager_v1_scheduling_api_proto_rawDescData []byte
)

func file_api_scheduling_manager_v1_scheduling_api_proto_rawDescGZIP() []byte {
	file_api_scheduling_manager_v1_scheduling_api_proto_rawDescOnce.Do(func() {
		file_api_scheduling_manager_v1_scheduling_api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_scheduling_manager_v1_scheduling_api_proto_rawDesc), len(file_api_scheduling_manager_v1_scheduling_api_proto_rawDesc)))
	})
	return file_api_scheduling_manager_v1_scheduling_api_proto_rawDescData
}

var file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_api_scheduling_manager_v1_scheduling_api_proto_goTypes = []any{
	(RestartPolicy_Type)(0),                   // 0: api.scheduling_manager.v1.RestartPolicy.Type
	(ExtendedResource_Type)(0),                // 1: api.scheduling_manager.v1.ExtendedResource.Type
	(ResourcePool_Type)(0),                    // 2: api.scheduling_manager.v1.ResourcePool.Type
	(*RestartPolicy)(nil),                     // 3: api.scheduling_manager.v1.RestartPolicy
	(*ExtendedResource)(nil),                  // 4: api.scheduling_manager.v1.ExtendedResource
	(*Cpu)(nil),                               // 5: api.scheduling_manager.v1.Cpu
	(*ResourceClaim)(nil),                     // 6: api.scheduling_manager.v1.ResourceClaim
	(*Bucket)(nil),                            // 7: api.scheduling_manager.v1.Bucket
	(*BucketMount)(nil),                       // 8: api.scheduling_manager.v1.BucketMount
	(*Container)(nil),                         // 9: api.scheduling_manager.v1.Container
	(*CreatePodRequest)(nil),                  // 10: api.scheduling_manager.v1.CreatePodRequest
	(*CreatePodReply)(nil),                    // 11: api.scheduling_manager.v1.CreatePodReply
	(*CreatePodsRequest)(nil),                 // 12: api.scheduling_manager.v1.CreatePodsRequest
	(*CreatePodsReply)(nil),                   // 13: api.scheduling_manager.v1.CreatePodsReply
	(*QueryPodCountReply)(nil),                // 14: api.scheduling_manager.v1.QueryPodCountReply
	(*ResourcePool)(nil),                      // 15: api.scheduling_manager.v1.ResourcePool
	(*CreateTenantRequest)(nil),               // 16: api.scheduling_manager.v1.CreateTenantRequest
	(*CreateTenantReply)(nil),                 // 17: api.scheduling_manager.v1.CreateTenantReply
	(*RemoveTenantRequest)(nil),               // 18: api.scheduling_manager.v1.RemoveTenantRequest
	(*CreateWorkspaceRequest)(nil),            // 19: api.scheduling_manager.v1.CreateWorkspaceRequest
	(*CreateWorkspaceReply)(nil),              // 20: api.scheduling_manager.v1.CreateWorkspaceReply
	(*RemoveWorkspaceRequest)(nil),            // 21: api.scheduling_manager.v1.RemoveWorkspaceRequest
	(*LabelNodeRequest)(nil),                  // 22: api.scheduling_manager.v1.LabelNodeRequest
	(*UnlabelNodeRequest)(nil),                // 23: api.scheduling_manager.v1.UnlabelNodeRequest
	(*AllocateNodeToTenantRequest)(nil),       // 24: api.scheduling_manager.v1.AllocateNodeToTenantRequest
	(*AllocateNodeToWorkspaceRequest)(nil),    // 25: api.scheduling_manager.v1.AllocateNodeToWorkspaceRequest
	(*AllocateNodeToResourcePoolRequest)(nil), // 26: api.scheduling_manager.v1.AllocateNodeToResourcePoolRequest
	(*ResourceAvailableRequest)(nil),          // 27: api.scheduling_manager.v1.ResourceAvailableRequest
	(*ResourceAvailableReply)(nil),            // 28: api.scheduling_manager.v1.ResourceAvailableReply
	nil,                                       // 29: api.scheduling_manager.v1.ResourceClaim.ExtendedResourceEntry
	nil,                                       // 30: api.scheduling_manager.v1.CreatePodsReply.PodIdMapEntry
	nil,                                       // 31: api.scheduling_manager.v1.CreateTenantRequest.QuotasEntry
	nil,                                       // 32: api.scheduling_manager.v1.CreateTenantReply.NamespacesEntry
	nil,                                       // 33: api.scheduling_manager.v1.CreateWorkspaceRequest.QuotasEntry
	nil,                                       // 34: api.scheduling_manager.v1.CreateWorkspaceReply.NamespacesEntry
	(BilingMode_Type)(0),                      // 35: api.scheduling_manager.v1.BilingMode.Type
	(*SpecConfiguration)(nil),                 // 36: api.scheduling_manager.v1.SpecConfiguration
	(*emptypb.Empty)(nil),                     // 37: google.protobuf.Empty
}
var file_api_scheduling_manager_v1_scheduling_api_proto_depIdxs = []int32{
	5,  // 0: api.scheduling_manager.v1.ResourceClaim.cpu:type_name -> api.scheduling_manager.v1.Cpu
	29, // 1: api.scheduling_manager.v1.ResourceClaim.extended_resource:type_name -> api.scheduling_manager.v1.ResourceClaim.ExtendedResourceEntry
	6,  // 2: api.scheduling_manager.v1.Container.resource:type_name -> api.scheduling_manager.v1.ResourceClaim
	8,  // 3: api.scheduling_manager.v1.Container.buckets:type_name -> api.scheduling_manager.v1.BucketMount
	0,  // 4: api.scheduling_manager.v1.CreatePodRequest.restart_policy:type_name -> api.scheduling_manager.v1.RestartPolicy.Type
	9,  // 5: api.scheduling_manager.v1.CreatePodRequest.containers:type_name -> api.scheduling_manager.v1.Container
	7,  // 6: api.scheduling_manager.v1.CreatePodRequest.buckets:type_name -> api.scheduling_manager.v1.Bucket
	35, // 7: api.scheduling_manager.v1.CreatePodRequest.billing_mode:type_name -> api.scheduling_manager.v1.BilingMode.Type
	10, // 8: api.scheduling_manager.v1.CreatePodsRequest.requests:type_name -> api.scheduling_manager.v1.CreatePodRequest
	30, // 9: api.scheduling_manager.v1.CreatePodsReply.pod_id_map:type_name -> api.scheduling_manager.v1.CreatePodsReply.PodIdMapEntry
	31, // 10: api.scheduling_manager.v1.CreateTenantRequest.quotas:type_name -> api.scheduling_manager.v1.CreateTenantRequest.QuotasEntry
	32, // 11: api.scheduling_manager.v1.CreateTenantReply.namespaces:type_name -> api.scheduling_manager.v1.CreateTenantReply.NamespacesEntry
	33, // 12: api.scheduling_manager.v1.CreateWorkspaceRequest.quotas:type_name -> api.scheduling_manager.v1.CreateWorkspaceRequest.QuotasEntry
	34, // 13: api.scheduling_manager.v1.CreateWorkspaceReply.namespaces:type_name -> api.scheduling_manager.v1.CreateWorkspaceReply.NamespacesEntry
	2,  // 14: api.scheduling_manager.v1.AllocateNodeToResourcePoolRequest.pool:type_name -> api.scheduling_manager.v1.ResourcePool.Type
	2,  // 15: api.scheduling_manager.v1.ResourceAvailableRequest.pool:type_name -> api.scheduling_manager.v1.ResourcePool.Type
	36, // 16: api.scheduling_manager.v1.ResourceAvailableRequest.configurations:type_name -> api.scheduling_manager.v1.SpecConfiguration
	6,  // 17: api.scheduling_manager.v1.CreateTenantRequest.QuotasEntry.value:type_name -> api.scheduling_manager.v1.ResourceClaim
	6,  // 18: api.scheduling_manager.v1.CreateWorkspaceRequest.QuotasEntry.value:type_name -> api.scheduling_manager.v1.ResourceClaim
	10, // 19: api.scheduling_manager.v1.SchedulingApi.CreatePod:input_type -> api.scheduling_manager.v1.CreatePodRequest
	12, // 20: api.scheduling_manager.v1.SchedulingApi.CreatePods:input_type -> api.scheduling_manager.v1.CreatePodsRequest
	10, // 21: api.scheduling_manager.v1.SchedulingApi.QueryPodCount:input_type -> api.scheduling_manager.v1.CreatePodRequest
	16, // 22: api.scheduling_manager.v1.SchedulingApi.CreateTenant:input_type -> api.scheduling_manager.v1.CreateTenantRequest
	18, // 23: api.scheduling_manager.v1.SchedulingApi.RemoveTenant:input_type -> api.scheduling_manager.v1.RemoveTenantRequest
	16, // 24: api.scheduling_manager.v1.SchedulingApi.UpdateTenant:input_type -> api.scheduling_manager.v1.CreateTenantRequest
	16, // 25: api.scheduling_manager.v1.SchedulingApi.SyncTenant:input_type -> api.scheduling_manager.v1.CreateTenantRequest
	19, // 26: api.scheduling_manager.v1.SchedulingApi.CreateWorkspace:input_type -> api.scheduling_manager.v1.CreateWorkspaceRequest
	21, // 27: api.scheduling_manager.v1.SchedulingApi.RemoveWorkspace:input_type -> api.scheduling_manager.v1.RemoveWorkspaceRequest
	19, // 28: api.scheduling_manager.v1.SchedulingApi.UpdateWorkspace:input_type -> api.scheduling_manager.v1.CreateWorkspaceRequest
	19, // 29: api.scheduling_manager.v1.SchedulingApi.SyncWorkspace:input_type -> api.scheduling_manager.v1.CreateWorkspaceRequest
	22, // 30: api.scheduling_manager.v1.SchedulingApi.LableNode:input_type -> api.scheduling_manager.v1.LabelNodeRequest
	23, // 31: api.scheduling_manager.v1.SchedulingApi.UnlabelNode:input_type -> api.scheduling_manager.v1.UnlabelNodeRequest
	24, // 32: api.scheduling_manager.v1.SchedulingApi.AllocateNodeToTenant:input_type -> api.scheduling_manager.v1.AllocateNodeToTenantRequest
	24, // 33: api.scheduling_manager.v1.SchedulingApi.DeallocateNodeToTenant:input_type -> api.scheduling_manager.v1.AllocateNodeToTenantRequest
	25, // 34: api.scheduling_manager.v1.SchedulingApi.AllocateNodeToWorkspace:input_type -> api.scheduling_manager.v1.AllocateNodeToWorkspaceRequest
	25, // 35: api.scheduling_manager.v1.SchedulingApi.DeallocateNodeToWorkspace:input_type -> api.scheduling_manager.v1.AllocateNodeToWorkspaceRequest
	26, // 36: api.scheduling_manager.v1.SchedulingApi.AllocateNodeToResourcePool:input_type -> api.scheduling_manager.v1.AllocateNodeToResourcePoolRequest
	26, // 37: api.scheduling_manager.v1.SchedulingApi.DeallocateNodeToResourcePool:input_type -> api.scheduling_manager.v1.AllocateNodeToResourcePoolRequest
	27, // 38: api.scheduling_manager.v1.SchedulingApi.ResourceAvailable:input_type -> api.scheduling_manager.v1.ResourceAvailableRequest
	11, // 39: api.scheduling_manager.v1.SchedulingApi.CreatePod:output_type -> api.scheduling_manager.v1.CreatePodReply
	13, // 40: api.scheduling_manager.v1.SchedulingApi.CreatePods:output_type -> api.scheduling_manager.v1.CreatePodsReply
	14, // 41: api.scheduling_manager.v1.SchedulingApi.QueryPodCount:output_type -> api.scheduling_manager.v1.QueryPodCountReply
	17, // 42: api.scheduling_manager.v1.SchedulingApi.CreateTenant:output_type -> api.scheduling_manager.v1.CreateTenantReply
	37, // 43: api.scheduling_manager.v1.SchedulingApi.RemoveTenant:output_type -> google.protobuf.Empty
	37, // 44: api.scheduling_manager.v1.SchedulingApi.UpdateTenant:output_type -> google.protobuf.Empty
	17, // 45: api.scheduling_manager.v1.SchedulingApi.SyncTenant:output_type -> api.scheduling_manager.v1.CreateTenantReply
	20, // 46: api.scheduling_manager.v1.SchedulingApi.CreateWorkspace:output_type -> api.scheduling_manager.v1.CreateWorkspaceReply
	37, // 47: api.scheduling_manager.v1.SchedulingApi.RemoveWorkspace:output_type -> google.protobuf.Empty
	37, // 48: api.scheduling_manager.v1.SchedulingApi.UpdateWorkspace:output_type -> google.protobuf.Empty
	20, // 49: api.scheduling_manager.v1.SchedulingApi.SyncWorkspace:output_type -> api.scheduling_manager.v1.CreateWorkspaceReply
	37, // 50: api.scheduling_manager.v1.SchedulingApi.LableNode:output_type -> google.protobuf.Empty
	37, // 51: api.scheduling_manager.v1.SchedulingApi.UnlabelNode:output_type -> google.protobuf.Empty
	37, // 52: api.scheduling_manager.v1.SchedulingApi.AllocateNodeToTenant:output_type -> google.protobuf.Empty
	37, // 53: api.scheduling_manager.v1.SchedulingApi.DeallocateNodeToTenant:output_type -> google.protobuf.Empty
	37, // 54: api.scheduling_manager.v1.SchedulingApi.AllocateNodeToWorkspace:output_type -> google.protobuf.Empty
	37, // 55: api.scheduling_manager.v1.SchedulingApi.DeallocateNodeToWorkspace:output_type -> google.protobuf.Empty
	37, // 56: api.scheduling_manager.v1.SchedulingApi.AllocateNodeToResourcePool:output_type -> google.protobuf.Empty
	37, // 57: api.scheduling_manager.v1.SchedulingApi.DeallocateNodeToResourcePool:output_type -> google.protobuf.Empty
	28, // 58: api.scheduling_manager.v1.SchedulingApi.ResourceAvailable:output_type -> api.scheduling_manager.v1.ResourceAvailableReply
	39, // [39:59] is the sub-list for method output_type
	19, // [19:39] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_scheduling_manager_v1_scheduling_api_proto_init() }
func file_api_scheduling_manager_v1_scheduling_api_proto_init() {
	if File_api_scheduling_manager_v1_scheduling_api_proto != nil {
		return
	}
	file_api_scheduling_manager_v1_billing_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_scheduling_manager_v1_scheduling_api_proto_rawDesc), len(file_api_scheduling_manager_v1_scheduling_api_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_scheduling_manager_v1_scheduling_api_proto_goTypes,
		DependencyIndexes: file_api_scheduling_manager_v1_scheduling_api_proto_depIdxs,
		EnumInfos:         file_api_scheduling_manager_v1_scheduling_api_proto_enumTypes,
		MessageInfos:      file_api_scheduling_manager_v1_scheduling_api_proto_msgTypes,
	}.Build()
	File_api_scheduling_manager_v1_scheduling_api_proto = out.File
	file_api_scheduling_manager_v1_scheduling_api_proto_goTypes = nil
	file_api_scheduling_manager_v1_scheduling_api_proto_depIdxs = nil
}
