// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/scheduling-manager/v1/scheduling-api.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SchedulingApi_CreatePod_FullMethodName                    = "/api.scheduling_manager.v1.SchedulingApi/CreatePod"
	SchedulingApi_CreatePods_FullMethodName                   = "/api.scheduling_manager.v1.SchedulingApi/CreatePods"
	SchedulingApi_QueryPodCount_FullMethodName                = "/api.scheduling_manager.v1.SchedulingApi/QueryPodCount"
	SchedulingApi_CreateTenant_FullMethodName                 = "/api.scheduling_manager.v1.SchedulingApi/CreateTenant"
	SchedulingApi_RemoveTenant_FullMethodName                 = "/api.scheduling_manager.v1.SchedulingApi/RemoveTenant"
	SchedulingApi_UpdateTenant_FullMethodName                 = "/api.scheduling_manager.v1.SchedulingApi/UpdateTenant"
	SchedulingApi_SyncTenant_FullMethodName                   = "/api.scheduling_manager.v1.SchedulingApi/SyncTenant"
	SchedulingApi_CreateWorkspace_FullMethodName              = "/api.scheduling_manager.v1.SchedulingApi/CreateWorkspace"
	SchedulingApi_RemoveWorkspace_FullMethodName              = "/api.scheduling_manager.v1.SchedulingApi/RemoveWorkspace"
	SchedulingApi_UpdateWorkspace_FullMethodName              = "/api.scheduling_manager.v1.SchedulingApi/UpdateWorkspace"
	SchedulingApi_SyncWorkspace_FullMethodName                = "/api.scheduling_manager.v1.SchedulingApi/SyncWorkspace"
	SchedulingApi_LableNode_FullMethodName                    = "/api.scheduling_manager.v1.SchedulingApi/LableNode"
	SchedulingApi_UnlabelNode_FullMethodName                  = "/api.scheduling_manager.v1.SchedulingApi/UnlabelNode"
	SchedulingApi_AllocateNodeToTenant_FullMethodName         = "/api.scheduling_manager.v1.SchedulingApi/AllocateNodeToTenant"
	SchedulingApi_DeallocateNodeToTenant_FullMethodName       = "/api.scheduling_manager.v1.SchedulingApi/DeallocateNodeToTenant"
	SchedulingApi_AllocateNodeToWorkspace_FullMethodName      = "/api.scheduling_manager.v1.SchedulingApi/AllocateNodeToWorkspace"
	SchedulingApi_DeallocateNodeToWorkspace_FullMethodName    = "/api.scheduling_manager.v1.SchedulingApi/DeallocateNodeToWorkspace"
	SchedulingApi_AllocateNodeToResourcePool_FullMethodName   = "/api.scheduling_manager.v1.SchedulingApi/AllocateNodeToResourcePool"
	SchedulingApi_DeallocateNodeToResourcePool_FullMethodName = "/api.scheduling_manager.v1.SchedulingApi/DeallocateNodeToResourcePool"
	SchedulingApi_ResourceAvailable_FullMethodName            = "/api.scheduling_manager.v1.SchedulingApi/ResourceAvailable"
)

// SchedulingApiClient is the client API for SchedulingApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The scheduling api service definition.
type SchedulingApiClient interface {
	// pod & pods
	CreatePod(ctx context.Context, in *CreatePodRequest, opts ...grpc.CallOption) (*CreatePodReply, error)
	CreatePods(ctx context.Context, in *CreatePodsRequest, opts ...grpc.CallOption) (*CreatePodsReply, error)
	QueryPodCount(ctx context.Context, in *CreatePodRequest, opts ...grpc.CallOption) (*QueryPodCountReply, error)
	// tenants
	CreateTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*CreateTenantReply, error)
	RemoveTenant(ctx context.Context, in *RemoveTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SyncTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*CreateTenantReply, error)
	// workspace
	CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*CreateWorkspaceReply, error)
	RemoveWorkspace(ctx context.Context, in *RemoveWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SyncWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*CreateWorkspaceReply, error)
	// nodes
	LableNode(ctx context.Context, in *LabelNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UnlabelNode(ctx context.Context, in *UnlabelNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	AllocateNodeToTenant(ctx context.Context, in *AllocateNodeToTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeallocateNodeToTenant(ctx context.Context, in *AllocateNodeToTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	AllocateNodeToWorkspace(ctx context.Context, in *AllocateNodeToWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeallocateNodeToWorkspace(ctx context.Context, in *AllocateNodeToWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	AllocateNodeToResourcePool(ctx context.Context, in *AllocateNodeToResourcePoolRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeallocateNodeToResourcePool(ctx context.Context, in *AllocateNodeToResourcePoolRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// resource pool
	ResourceAvailable(ctx context.Context, in *ResourceAvailableRequest, opts ...grpc.CallOption) (*ResourceAvailableReply, error)
}

type schedulingApiClient struct {
	cc grpc.ClientConnInterface
}

func NewSchedulingApiClient(cc grpc.ClientConnInterface) SchedulingApiClient {
	return &schedulingApiClient{cc}
}

func (c *schedulingApiClient) CreatePod(ctx context.Context, in *CreatePodRequest, opts ...grpc.CallOption) (*CreatePodReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePodReply)
	err := c.cc.Invoke(ctx, SchedulingApi_CreatePod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) CreatePods(ctx context.Context, in *CreatePodsRequest, opts ...grpc.CallOption) (*CreatePodsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePodsReply)
	err := c.cc.Invoke(ctx, SchedulingApi_CreatePods_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) QueryPodCount(ctx context.Context, in *CreatePodRequest, opts ...grpc.CallOption) (*QueryPodCountReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryPodCountReply)
	err := c.cc.Invoke(ctx, SchedulingApi_QueryPodCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) CreateTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*CreateTenantReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTenantReply)
	err := c.cc.Invoke(ctx, SchedulingApi_CreateTenant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) RemoveTenant(ctx context.Context, in *RemoveTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_RemoveTenant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) UpdateTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_UpdateTenant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) SyncTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*CreateTenantReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTenantReply)
	err := c.cc.Invoke(ctx, SchedulingApi_SyncTenant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*CreateWorkspaceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateWorkspaceReply)
	err := c.cc.Invoke(ctx, SchedulingApi_CreateWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) RemoveWorkspace(ctx context.Context, in *RemoveWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_RemoveWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) UpdateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_UpdateWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) SyncWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...grpc.CallOption) (*CreateWorkspaceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateWorkspaceReply)
	err := c.cc.Invoke(ctx, SchedulingApi_SyncWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) LableNode(ctx context.Context, in *LabelNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_LableNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) UnlabelNode(ctx context.Context, in *UnlabelNodeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_UnlabelNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) AllocateNodeToTenant(ctx context.Context, in *AllocateNodeToTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_AllocateNodeToTenant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) DeallocateNodeToTenant(ctx context.Context, in *AllocateNodeToTenantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_DeallocateNodeToTenant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) AllocateNodeToWorkspace(ctx context.Context, in *AllocateNodeToWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_AllocateNodeToWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) DeallocateNodeToWorkspace(ctx context.Context, in *AllocateNodeToWorkspaceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_DeallocateNodeToWorkspace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) AllocateNodeToResourcePool(ctx context.Context, in *AllocateNodeToResourcePoolRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_AllocateNodeToResourcePool_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) DeallocateNodeToResourcePool(ctx context.Context, in *AllocateNodeToResourcePoolRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchedulingApi_DeallocateNodeToResourcePool_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingApiClient) ResourceAvailable(ctx context.Context, in *ResourceAvailableRequest, opts ...grpc.CallOption) (*ResourceAvailableReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResourceAvailableReply)
	err := c.cc.Invoke(ctx, SchedulingApi_ResourceAvailable_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SchedulingApiServer is the server API for SchedulingApi service.
// All implementations must embed UnimplementedSchedulingApiServer
// for forward compatibility.
//
// The scheduling api service definition.
type SchedulingApiServer interface {
	// pod & pods
	CreatePod(context.Context, *CreatePodRequest) (*CreatePodReply, error)
	CreatePods(context.Context, *CreatePodsRequest) (*CreatePodsReply, error)
	QueryPodCount(context.Context, *CreatePodRequest) (*QueryPodCountReply, error)
	// tenants
	CreateTenant(context.Context, *CreateTenantRequest) (*CreateTenantReply, error)
	RemoveTenant(context.Context, *RemoveTenantRequest) (*emptypb.Empty, error)
	UpdateTenant(context.Context, *CreateTenantRequest) (*emptypb.Empty, error)
	SyncTenant(context.Context, *CreateTenantRequest) (*CreateTenantReply, error)
	// workspace
	CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceReply, error)
	RemoveWorkspace(context.Context, *RemoveWorkspaceRequest) (*emptypb.Empty, error)
	UpdateWorkspace(context.Context, *CreateWorkspaceRequest) (*emptypb.Empty, error)
	SyncWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceReply, error)
	// nodes
	LableNode(context.Context, *LabelNodeRequest) (*emptypb.Empty, error)
	UnlabelNode(context.Context, *UnlabelNodeRequest) (*emptypb.Empty, error)
	AllocateNodeToTenant(context.Context, *AllocateNodeToTenantRequest) (*emptypb.Empty, error)
	DeallocateNodeToTenant(context.Context, *AllocateNodeToTenantRequest) (*emptypb.Empty, error)
	AllocateNodeToWorkspace(context.Context, *AllocateNodeToWorkspaceRequest) (*emptypb.Empty, error)
	DeallocateNodeToWorkspace(context.Context, *AllocateNodeToWorkspaceRequest) (*emptypb.Empty, error)
	AllocateNodeToResourcePool(context.Context, *AllocateNodeToResourcePoolRequest) (*emptypb.Empty, error)
	DeallocateNodeToResourcePool(context.Context, *AllocateNodeToResourcePoolRequest) (*emptypb.Empty, error)
	// resource pool
	ResourceAvailable(context.Context, *ResourceAvailableRequest) (*ResourceAvailableReply, error)
	mustEmbedUnimplementedSchedulingApiServer()
}

// UnimplementedSchedulingApiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSchedulingApiServer struct{}

func (UnimplementedSchedulingApiServer) CreatePod(context.Context, *CreatePodRequest) (*CreatePodReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePod not implemented")
}
func (UnimplementedSchedulingApiServer) CreatePods(context.Context, *CreatePodsRequest) (*CreatePodsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePods not implemented")
}
func (UnimplementedSchedulingApiServer) QueryPodCount(context.Context, *CreatePodRequest) (*QueryPodCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPodCount not implemented")
}
func (UnimplementedSchedulingApiServer) CreateTenant(context.Context, *CreateTenantRequest) (*CreateTenantReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTenant not implemented")
}
func (UnimplementedSchedulingApiServer) RemoveTenant(context.Context, *RemoveTenantRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTenant not implemented")
}
func (UnimplementedSchedulingApiServer) UpdateTenant(context.Context, *CreateTenantRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTenant not implemented")
}
func (UnimplementedSchedulingApiServer) SyncTenant(context.Context, *CreateTenantRequest) (*CreateTenantReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncTenant not implemented")
}
func (UnimplementedSchedulingApiServer) CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkspace not implemented")
}
func (UnimplementedSchedulingApiServer) RemoveWorkspace(context.Context, *RemoveWorkspaceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveWorkspace not implemented")
}
func (UnimplementedSchedulingApiServer) UpdateWorkspace(context.Context, *CreateWorkspaceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkspace not implemented")
}
func (UnimplementedSchedulingApiServer) SyncWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncWorkspace not implemented")
}
func (UnimplementedSchedulingApiServer) LableNode(context.Context, *LabelNodeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LableNode not implemented")
}
func (UnimplementedSchedulingApiServer) UnlabelNode(context.Context, *UnlabelNodeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlabelNode not implemented")
}
func (UnimplementedSchedulingApiServer) AllocateNodeToTenant(context.Context, *AllocateNodeToTenantRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllocateNodeToTenant not implemented")
}
func (UnimplementedSchedulingApiServer) DeallocateNodeToTenant(context.Context, *AllocateNodeToTenantRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeallocateNodeToTenant not implemented")
}
func (UnimplementedSchedulingApiServer) AllocateNodeToWorkspace(context.Context, *AllocateNodeToWorkspaceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllocateNodeToWorkspace not implemented")
}
func (UnimplementedSchedulingApiServer) DeallocateNodeToWorkspace(context.Context, *AllocateNodeToWorkspaceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeallocateNodeToWorkspace not implemented")
}
func (UnimplementedSchedulingApiServer) AllocateNodeToResourcePool(context.Context, *AllocateNodeToResourcePoolRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllocateNodeToResourcePool not implemented")
}
func (UnimplementedSchedulingApiServer) DeallocateNodeToResourcePool(context.Context, *AllocateNodeToResourcePoolRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeallocateNodeToResourcePool not implemented")
}
func (UnimplementedSchedulingApiServer) ResourceAvailable(context.Context, *ResourceAvailableRequest) (*ResourceAvailableReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResourceAvailable not implemented")
}
func (UnimplementedSchedulingApiServer) mustEmbedUnimplementedSchedulingApiServer() {}
func (UnimplementedSchedulingApiServer) testEmbeddedByValue()                       {}

// UnsafeSchedulingApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SchedulingApiServer will
// result in compilation errors.
type UnsafeSchedulingApiServer interface {
	mustEmbedUnimplementedSchedulingApiServer()
}

func RegisterSchedulingApiServer(s grpc.ServiceRegistrar, srv SchedulingApiServer) {
	// If the following call pancis, it indicates UnimplementedSchedulingApiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SchedulingApi_ServiceDesc, srv)
}

func _SchedulingApi_CreatePod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).CreatePod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_CreatePod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).CreatePod(ctx, req.(*CreatePodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_CreatePods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).CreatePods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_CreatePods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).CreatePods(ctx, req.(*CreatePodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_QueryPodCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).QueryPodCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_QueryPodCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).QueryPodCount(ctx, req.(*CreatePodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_CreateTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).CreateTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_CreateTenant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).CreateTenant(ctx, req.(*CreateTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_RemoveTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).RemoveTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_RemoveTenant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).RemoveTenant(ctx, req.(*RemoveTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_UpdateTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).UpdateTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_UpdateTenant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).UpdateTenant(ctx, req.(*CreateTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_SyncTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).SyncTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_SyncTenant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).SyncTenant(ctx, req.(*CreateTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_CreateWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).CreateWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_CreateWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).CreateWorkspace(ctx, req.(*CreateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_RemoveWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).RemoveWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_RemoveWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).RemoveWorkspace(ctx, req.(*RemoveWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_UpdateWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).UpdateWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_UpdateWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).UpdateWorkspace(ctx, req.(*CreateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_SyncWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).SyncWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_SyncWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).SyncWorkspace(ctx, req.(*CreateWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_LableNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).LableNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_LableNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).LableNode(ctx, req.(*LabelNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_UnlabelNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlabelNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).UnlabelNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_UnlabelNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).UnlabelNode(ctx, req.(*UnlabelNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_AllocateNodeToTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateNodeToTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).AllocateNodeToTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_AllocateNodeToTenant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).AllocateNodeToTenant(ctx, req.(*AllocateNodeToTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_DeallocateNodeToTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateNodeToTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).DeallocateNodeToTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_DeallocateNodeToTenant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).DeallocateNodeToTenant(ctx, req.(*AllocateNodeToTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_AllocateNodeToWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateNodeToWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).AllocateNodeToWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_AllocateNodeToWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).AllocateNodeToWorkspace(ctx, req.(*AllocateNodeToWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_DeallocateNodeToWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateNodeToWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).DeallocateNodeToWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_DeallocateNodeToWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).DeallocateNodeToWorkspace(ctx, req.(*AllocateNodeToWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_AllocateNodeToResourcePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateNodeToResourcePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).AllocateNodeToResourcePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_AllocateNodeToResourcePool_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).AllocateNodeToResourcePool(ctx, req.(*AllocateNodeToResourcePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_DeallocateNodeToResourcePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateNodeToResourcePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).DeallocateNodeToResourcePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_DeallocateNodeToResourcePool_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).DeallocateNodeToResourcePool(ctx, req.(*AllocateNodeToResourcePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchedulingApi_ResourceAvailable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResourceAvailableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingApiServer).ResourceAvailable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchedulingApi_ResourceAvailable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingApiServer).ResourceAvailable(ctx, req.(*ResourceAvailableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SchedulingApi_ServiceDesc is the grpc.ServiceDesc for SchedulingApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SchedulingApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.scheduling_manager.v1.SchedulingApi",
	HandlerType: (*SchedulingApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePod",
			Handler:    _SchedulingApi_CreatePod_Handler,
		},
		{
			MethodName: "CreatePods",
			Handler:    _SchedulingApi_CreatePods_Handler,
		},
		{
			MethodName: "QueryPodCount",
			Handler:    _SchedulingApi_QueryPodCount_Handler,
		},
		{
			MethodName: "CreateTenant",
			Handler:    _SchedulingApi_CreateTenant_Handler,
		},
		{
			MethodName: "RemoveTenant",
			Handler:    _SchedulingApi_RemoveTenant_Handler,
		},
		{
			MethodName: "UpdateTenant",
			Handler:    _SchedulingApi_UpdateTenant_Handler,
		},
		{
			MethodName: "SyncTenant",
			Handler:    _SchedulingApi_SyncTenant_Handler,
		},
		{
			MethodName: "CreateWorkspace",
			Handler:    _SchedulingApi_CreateWorkspace_Handler,
		},
		{
			MethodName: "RemoveWorkspace",
			Handler:    _SchedulingApi_RemoveWorkspace_Handler,
		},
		{
			MethodName: "UpdateWorkspace",
			Handler:    _SchedulingApi_UpdateWorkspace_Handler,
		},
		{
			MethodName: "SyncWorkspace",
			Handler:    _SchedulingApi_SyncWorkspace_Handler,
		},
		{
			MethodName: "LableNode",
			Handler:    _SchedulingApi_LableNode_Handler,
		},
		{
			MethodName: "UnlabelNode",
			Handler:    _SchedulingApi_UnlabelNode_Handler,
		},
		{
			MethodName: "AllocateNodeToTenant",
			Handler:    _SchedulingApi_AllocateNodeToTenant_Handler,
		},
		{
			MethodName: "DeallocateNodeToTenant",
			Handler:    _SchedulingApi_DeallocateNodeToTenant_Handler,
		},
		{
			MethodName: "AllocateNodeToWorkspace",
			Handler:    _SchedulingApi_AllocateNodeToWorkspace_Handler,
		},
		{
			MethodName: "DeallocateNodeToWorkspace",
			Handler:    _SchedulingApi_DeallocateNodeToWorkspace_Handler,
		},
		{
			MethodName: "AllocateNodeToResourcePool",
			Handler:    _SchedulingApi_AllocateNodeToResourcePool_Handler,
		},
		{
			MethodName: "DeallocateNodeToResourcePool",
			Handler:    _SchedulingApi_DeallocateNodeToResourcePool_Handler,
		},
		{
			MethodName: "ResourceAvailable",
			Handler:    _SchedulingApi_ResourceAvailable_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/scheduling-manager/v1/scheduling-api.proto",
}
