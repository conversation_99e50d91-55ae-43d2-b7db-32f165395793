// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.12.4
// source: api/scheduling-manager/v1/scheduling-api.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSchedulingApiAllocateNodeToResourcePool = "/api.scheduling_manager.v1.SchedulingApi/AllocateNodeToResourcePool"
const OperationSchedulingApiAllocateNodeToTenant = "/api.scheduling_manager.v1.SchedulingApi/AllocateNodeToTenant"
const OperationSchedulingApiAllocateNodeToWorkspace = "/api.scheduling_manager.v1.SchedulingApi/AllocateNodeToWorkspace"
const OperationSchedulingApiCreatePod = "/api.scheduling_manager.v1.SchedulingApi/CreatePod"
const OperationSchedulingApiCreatePods = "/api.scheduling_manager.v1.SchedulingApi/CreatePods"
const OperationSchedulingApiCreateTenant = "/api.scheduling_manager.v1.SchedulingApi/CreateTenant"
const OperationSchedulingApiCreateWorkspace = "/api.scheduling_manager.v1.SchedulingApi/CreateWorkspace"
const OperationSchedulingApiDeallocateNodeToResourcePool = "/api.scheduling_manager.v1.SchedulingApi/DeallocateNodeToResourcePool"
const OperationSchedulingApiDeallocateNodeToTenant = "/api.scheduling_manager.v1.SchedulingApi/DeallocateNodeToTenant"
const OperationSchedulingApiDeallocateNodeToWorkspace = "/api.scheduling_manager.v1.SchedulingApi/DeallocateNodeToWorkspace"
const OperationSchedulingApiLableNode = "/api.scheduling_manager.v1.SchedulingApi/LableNode"
const OperationSchedulingApiQueryPodCount = "/api.scheduling_manager.v1.SchedulingApi/QueryPodCount"
const OperationSchedulingApiRemoveTenant = "/api.scheduling_manager.v1.SchedulingApi/RemoveTenant"
const OperationSchedulingApiRemoveWorkspace = "/api.scheduling_manager.v1.SchedulingApi/RemoveWorkspace"
const OperationSchedulingApiResourceAvailable = "/api.scheduling_manager.v1.SchedulingApi/ResourceAvailable"
const OperationSchedulingApiSyncTenant = "/api.scheduling_manager.v1.SchedulingApi/SyncTenant"
const OperationSchedulingApiSyncWorkspace = "/api.scheduling_manager.v1.SchedulingApi/SyncWorkspace"
const OperationSchedulingApiUnlabelNode = "/api.scheduling_manager.v1.SchedulingApi/UnlabelNode"
const OperationSchedulingApiUpdateTenant = "/api.scheduling_manager.v1.SchedulingApi/UpdateTenant"
const OperationSchedulingApiUpdateWorkspace = "/api.scheduling_manager.v1.SchedulingApi/UpdateWorkspace"

type SchedulingApiHTTPServer interface {
	AllocateNodeToResourcePool(context.Context, *AllocateNodeToResourcePoolRequest) (*emptypb.Empty, error)
	AllocateNodeToTenant(context.Context, *AllocateNodeToTenantRequest) (*emptypb.Empty, error)
	AllocateNodeToWorkspace(context.Context, *AllocateNodeToWorkspaceRequest) (*emptypb.Empty, error)
	// CreatePod pod & pods
	CreatePod(context.Context, *CreatePodRequest) (*CreatePodReply, error)
	CreatePods(context.Context, *CreatePodsRequest) (*CreatePodsReply, error)
	// CreateTenant tenants
	CreateTenant(context.Context, *CreateTenantRequest) (*CreateTenantReply, error)
	// CreateWorkspace workspace
	CreateWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceReply, error)
	DeallocateNodeToResourcePool(context.Context, *AllocateNodeToResourcePoolRequest) (*emptypb.Empty, error)
	DeallocateNodeToTenant(context.Context, *AllocateNodeToTenantRequest) (*emptypb.Empty, error)
	DeallocateNodeToWorkspace(context.Context, *AllocateNodeToWorkspaceRequest) (*emptypb.Empty, error)
	// LableNode nodes
	LableNode(context.Context, *LabelNodeRequest) (*emptypb.Empty, error)
	QueryPodCount(context.Context, *CreatePodRequest) (*QueryPodCountReply, error)
	RemoveTenant(context.Context, *RemoveTenantRequest) (*emptypb.Empty, error)
	RemoveWorkspace(context.Context, *RemoveWorkspaceRequest) (*emptypb.Empty, error)
	// ResourceAvailable resource pool
	ResourceAvailable(context.Context, *ResourceAvailableRequest) (*ResourceAvailableReply, error)
	SyncTenant(context.Context, *CreateTenantRequest) (*CreateTenantReply, error)
	SyncWorkspace(context.Context, *CreateWorkspaceRequest) (*CreateWorkspaceReply, error)
	UnlabelNode(context.Context, *UnlabelNodeRequest) (*emptypb.Empty, error)
	UpdateTenant(context.Context, *CreateTenantRequest) (*emptypb.Empty, error)
	UpdateWorkspace(context.Context, *CreateWorkspaceRequest) (*emptypb.Empty, error)
}

func RegisterSchedulingApiHTTPServer(s *http.Server, srv SchedulingApiHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/scheduling-manager/pod", _SchedulingApi_CreatePod0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/pods", _SchedulingApi_CreatePods0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/pod/count", _SchedulingApi_QueryPodCount0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/tenant", _SchedulingApi_CreateTenant0_HTTP_Handler(srv))
	r.DELETE("/v1/scheduling-manager/tenant/{tenant_id}", _SchedulingApi_RemoveTenant0_HTTP_Handler(srv))
	r.PATCH("/v1/scheduling-manager/tenant/{tenant_id}", _SchedulingApi_UpdateTenant0_HTTP_Handler(srv))
	r.PUT("/v1/scheduling-manager/tenant", _SchedulingApi_SyncTenant0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/workspace", _SchedulingApi_CreateWorkspace0_HTTP_Handler(srv))
	r.DELETE("/v1/scheduling-manager/workspace/{workspace_id}", _SchedulingApi_RemoveWorkspace0_HTTP_Handler(srv))
	r.PATCH("/v1/scheduling-manager/workspace/{workspace_id}", _SchedulingApi_UpdateWorkspace0_HTTP_Handler(srv))
	r.PUT("/v1/scheduling-manager/workspace", _SchedulingApi_SyncWorkspace0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/node", _SchedulingApi_LableNode0_HTTP_Handler(srv))
	r.DELETE("/v1/scheduling-manager/node", _SchedulingApi_UnlabelNode0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/node/tenant", _SchedulingApi_AllocateNodeToTenant0_HTTP_Handler(srv))
	r.DELETE("/v1/scheduling-manager/node/tenant", _SchedulingApi_DeallocateNodeToTenant0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/node/workspace", _SchedulingApi_AllocateNodeToWorkspace0_HTTP_Handler(srv))
	r.DELETE("/v1/scheduling-manager/node/workspace", _SchedulingApi_DeallocateNodeToWorkspace0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/node/pool", _SchedulingApi_AllocateNodeToResourcePool0_HTTP_Handler(srv))
	r.DELETE("/v1/scheduling-manager/node/pool", _SchedulingApi_DeallocateNodeToResourcePool0_HTTP_Handler(srv))
	r.POST("/v1/scheduling-manager/pool/resource/available", _SchedulingApi_ResourceAvailable0_HTTP_Handler(srv))
}

func _SchedulingApi_CreatePod0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatePodRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiCreatePod)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePod(ctx, req.(*CreatePodRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreatePodReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_CreatePods0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatePodsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiCreatePods)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePods(ctx, req.(*CreatePodsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreatePodsReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_QueryPodCount0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatePodRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiQueryPodCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryPodCount(ctx, req.(*CreatePodRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryPodCountReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_CreateTenant0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTenantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiCreateTenant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTenant(ctx, req.(*CreateTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateTenantReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_RemoveTenant0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveTenantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiRemoveTenant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveTenant(ctx, req.(*RemoveTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_UpdateTenant0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTenantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiUpdateTenant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateTenant(ctx, req.(*CreateTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_SyncTenant0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTenantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiSyncTenant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncTenant(ctx, req.(*CreateTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateTenantReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_CreateWorkspace0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWorkspaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiCreateWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWorkspace(ctx, req.(*CreateWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateWorkspaceReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_RemoveWorkspace0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveWorkspaceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiRemoveWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveWorkspace(ctx, req.(*RemoveWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_UpdateWorkspace0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWorkspaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiUpdateWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWorkspace(ctx, req.(*CreateWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_SyncWorkspace0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWorkspaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiSyncWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncWorkspace(ctx, req.(*CreateWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateWorkspaceReply)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_LableNode0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LabelNodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiLableNode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LableNode(ctx, req.(*LabelNodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_UnlabelNode0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UnlabelNodeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiUnlabelNode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnlabelNode(ctx, req.(*UnlabelNodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_AllocateNodeToTenant0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllocateNodeToTenantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiAllocateNodeToTenant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AllocateNodeToTenant(ctx, req.(*AllocateNodeToTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_DeallocateNodeToTenant0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllocateNodeToTenantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiDeallocateNodeToTenant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeallocateNodeToTenant(ctx, req.(*AllocateNodeToTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_AllocateNodeToWorkspace0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllocateNodeToWorkspaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiAllocateNodeToWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AllocateNodeToWorkspace(ctx, req.(*AllocateNodeToWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_DeallocateNodeToWorkspace0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllocateNodeToWorkspaceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiDeallocateNodeToWorkspace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeallocateNodeToWorkspace(ctx, req.(*AllocateNodeToWorkspaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_AllocateNodeToResourcePool0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllocateNodeToResourcePoolRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiAllocateNodeToResourcePool)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AllocateNodeToResourcePool(ctx, req.(*AllocateNodeToResourcePoolRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_DeallocateNodeToResourcePool0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllocateNodeToResourcePoolRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiDeallocateNodeToResourcePool)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeallocateNodeToResourcePool(ctx, req.(*AllocateNodeToResourcePoolRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _SchedulingApi_ResourceAvailable0_HTTP_Handler(srv SchedulingApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResourceAvailableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSchedulingApiResourceAvailable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResourceAvailable(ctx, req.(*ResourceAvailableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResourceAvailableReply)
		return ctx.Result(200, reply)
	}
}

type SchedulingApiHTTPClient interface {
	AllocateNodeToResourcePool(ctx context.Context, req *AllocateNodeToResourcePoolRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AllocateNodeToTenant(ctx context.Context, req *AllocateNodeToTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AllocateNodeToWorkspace(ctx context.Context, req *AllocateNodeToWorkspaceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreatePod(ctx context.Context, req *CreatePodRequest, opts ...http.CallOption) (rsp *CreatePodReply, err error)
	CreatePods(ctx context.Context, req *CreatePodsRequest, opts ...http.CallOption) (rsp *CreatePodsReply, err error)
	CreateTenant(ctx context.Context, req *CreateTenantRequest, opts ...http.CallOption) (rsp *CreateTenantReply, err error)
	CreateWorkspace(ctx context.Context, req *CreateWorkspaceRequest, opts ...http.CallOption) (rsp *CreateWorkspaceReply, err error)
	DeallocateNodeToResourcePool(ctx context.Context, req *AllocateNodeToResourcePoolRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeallocateNodeToTenant(ctx context.Context, req *AllocateNodeToTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeallocateNodeToWorkspace(ctx context.Context, req *AllocateNodeToWorkspaceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	LableNode(ctx context.Context, req *LabelNodeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	QueryPodCount(ctx context.Context, req *CreatePodRequest, opts ...http.CallOption) (rsp *QueryPodCountReply, err error)
	RemoveTenant(ctx context.Context, req *RemoveTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RemoveWorkspace(ctx context.Context, req *RemoveWorkspaceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ResourceAvailable(ctx context.Context, req *ResourceAvailableRequest, opts ...http.CallOption) (rsp *ResourceAvailableReply, err error)
	SyncTenant(ctx context.Context, req *CreateTenantRequest, opts ...http.CallOption) (rsp *CreateTenantReply, err error)
	SyncWorkspace(ctx context.Context, req *CreateWorkspaceRequest, opts ...http.CallOption) (rsp *CreateWorkspaceReply, err error)
	UnlabelNode(ctx context.Context, req *UnlabelNodeRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateTenant(ctx context.Context, req *CreateTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateWorkspace(ctx context.Context, req *CreateWorkspaceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type SchedulingApiHTTPClientImpl struct {
	cc *http.Client
}

func NewSchedulingApiHTTPClient(client *http.Client) SchedulingApiHTTPClient {
	return &SchedulingApiHTTPClientImpl{client}
}

func (c *SchedulingApiHTTPClientImpl) AllocateNodeToResourcePool(ctx context.Context, in *AllocateNodeToResourcePoolRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node/pool"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiAllocateNodeToResourcePool))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) AllocateNodeToTenant(ctx context.Context, in *AllocateNodeToTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node/tenant"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiAllocateNodeToTenant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) AllocateNodeToWorkspace(ctx context.Context, in *AllocateNodeToWorkspaceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node/workspace"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiAllocateNodeToWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) CreatePod(ctx context.Context, in *CreatePodRequest, opts ...http.CallOption) (*CreatePodReply, error) {
	var out CreatePodReply
	pattern := "/v1/scheduling-manager/pod"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiCreatePod))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) CreatePods(ctx context.Context, in *CreatePodsRequest, opts ...http.CallOption) (*CreatePodsReply, error) {
	var out CreatePodsReply
	pattern := "/v1/scheduling-manager/pods"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiCreatePods))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) CreateTenant(ctx context.Context, in *CreateTenantRequest, opts ...http.CallOption) (*CreateTenantReply, error) {
	var out CreateTenantReply
	pattern := "/v1/scheduling-manager/tenant"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiCreateTenant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...http.CallOption) (*CreateWorkspaceReply, error) {
	var out CreateWorkspaceReply
	pattern := "/v1/scheduling-manager/workspace"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiCreateWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) DeallocateNodeToResourcePool(ctx context.Context, in *AllocateNodeToResourcePoolRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node/pool"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSchedulingApiDeallocateNodeToResourcePool))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) DeallocateNodeToTenant(ctx context.Context, in *AllocateNodeToTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node/tenant"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSchedulingApiDeallocateNodeToTenant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) DeallocateNodeToWorkspace(ctx context.Context, in *AllocateNodeToWorkspaceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node/workspace"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSchedulingApiDeallocateNodeToWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) LableNode(ctx context.Context, in *LabelNodeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiLableNode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) QueryPodCount(ctx context.Context, in *CreatePodRequest, opts ...http.CallOption) (*QueryPodCountReply, error) {
	var out QueryPodCountReply
	pattern := "/v1/scheduling-manager/pod/count"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiQueryPodCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) RemoveTenant(ctx context.Context, in *RemoveTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/tenant/{tenant_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSchedulingApiRemoveTenant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) RemoveWorkspace(ctx context.Context, in *RemoveWorkspaceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/workspace/{workspace_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSchedulingApiRemoveWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) ResourceAvailable(ctx context.Context, in *ResourceAvailableRequest, opts ...http.CallOption) (*ResourceAvailableReply, error) {
	var out ResourceAvailableReply
	pattern := "/v1/scheduling-manager/pool/resource/available"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiResourceAvailable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) SyncTenant(ctx context.Context, in *CreateTenantRequest, opts ...http.CallOption) (*CreateTenantReply, error) {
	var out CreateTenantReply
	pattern := "/v1/scheduling-manager/tenant"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiSyncTenant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) SyncWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...http.CallOption) (*CreateWorkspaceReply, error) {
	var out CreateWorkspaceReply
	pattern := "/v1/scheduling-manager/workspace"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiSyncWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) UnlabelNode(ctx context.Context, in *UnlabelNodeRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/node"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSchedulingApiUnlabelNode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) UpdateTenant(ctx context.Context, in *CreateTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/tenant/{tenant_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiUpdateTenant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SchedulingApiHTTPClientImpl) UpdateWorkspace(ctx context.Context, in *CreateWorkspaceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/scheduling-manager/workspace/{workspace_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSchedulingApiUpdateWorkspace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
