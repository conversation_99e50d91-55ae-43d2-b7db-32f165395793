syntax = "proto3";

package api.scheduling_manager.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "api/scheduling-manager/v1/billing.proto";

option go_package = "api/scheduling-manager/v1;v1";
option java_multiple_files = true;
option java_package = "dev.kratos.api.scheduling-manager.v1";
option java_outer_classname = "SchedulingManagerProtoV1";

// The scheduling api service definition.
service SchedulingApi {

  // pod & pods
  rpc CreatePod (CreatePodRequest) returns (CreatePodReply) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/pod"
      body: "*"
    };
  }

  rpc CreatePods (CreatePodsRequest) returns (CreatePodsReply) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/pods"
      body: "*"
    };
  }

  rpc QueryPodCount(CreatePodRequest) returns (QueryPodCountReply) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/pod/count"
      body: "*"
    };
  }

  // tenants
  rpc CreateTenant(CreateTenantRequest) returns (CreateTenantReply) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/tenant"
      body: "*"
    };
  }

  rpc RemoveTenant(RemoveTenantRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/scheduling-manager/tenant/{tenant_id}"
    };
  }

  rpc UpdateTenant(CreateTenantRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/v1/scheduling-manager/tenant/{tenant_id}"
      body: "*"
    };
  }

  rpc SyncTenant(CreateTenantRequest) returns (CreateTenantReply) {
    option (google.api.http) = {
      put: "/v1/scheduling-manager/tenant"
      body: "*"
    };
  }

  // workspace
  rpc CreateWorkspace(CreateWorkspaceRequest) returns (CreateWorkspaceReply) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/workspace"
      body: "*"
    };
  }

  rpc RemoveWorkspace(RemoveWorkspaceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/scheduling-manager/workspace/{workspace_id}"
    };
  }

  rpc UpdateWorkspace(CreateWorkspaceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/v1/scheduling-manager/workspace/{workspace_id}"
      body: "*"
    };
  }

  rpc SyncWorkspace(CreateWorkspaceRequest) returns (CreateWorkspaceReply){
    option (google.api.http) = {
      put: "/v1/scheduling-manager/workspace"
      body: "*"
    };
  }

  // nodes
  rpc LableNode(LabelNodeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/node"
      body: "*"
    };
  }

  rpc UnlabelNode(UnlabelNodeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/scheduling-manager/node"
    };
  }

  rpc AllocateNodeToTenant(AllocateNodeToTenantRequest) returns (
    google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/node/tenant"
      body: "*"
    };
  }

  rpc DeallocateNodeToTenant(AllocateNodeToTenantRequest) returns (
    google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/scheduling-manager/node/tenant"
    };
  }

  rpc AllocateNodeToWorkspace(AllocateNodeToWorkspaceRequest) returns (
    google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/node/workspace"
      body: "*"
    };
  }

  rpc DeallocateNodeToWorkspace(AllocateNodeToWorkspaceRequest) returns (
    google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/scheduling-manager/node/workspace"
    };
  }

  rpc AllocateNodeToResourcePool(AllocateNodeToResourcePoolRequest) returns (
    google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/node/pool"
      body: "*"
    };
  }

  rpc DeallocateNodeToResourcePool(AllocateNodeToResourcePoolRequest) returns (
    google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/scheduling-manager/node/pool"
    };
  }

  // resource pool
  rpc ResourceAvailable(ResourceAvailableRequest) returns (ResourceAvailableReply) {
    option (google.api.http) = {
      post: "/v1/scheduling-manager/pool/resource/available"
      body: "*"
    };
  }

}

message RestartPolicy {
  enum Type {
    UNKNOWN = 0;
    ALWAYS = 1;
    ON_FAILURE = 2;
    NEVER = 3;
  }
}

message ExtendedResource {
  enum Type {
    UNKNOWN = 0;
    GPU = 1;
    TECO = 2;
    TECOCORE = 3;
    DCU = 4;
  }
}

message Cpu {
  int64 num = 1;  // 1000, one core.
  string architecture = 2;
}

message ResourceClaim {
  Cpu cpu = 1;
  fixed64 memory = 2; // bytes
  // key is the int value of ExtendedResource.Type
  map<int32, fixed64> extended_resource = 3 [(validate.rules).map.keys.int32.lt = 5, (validate.rules).map.keys.int32.gt = 0];
}

message Bucket {
  string id = 1;
  string path = 2;
}

message BucketMount {
  string id = 1;
  string mnt_path = 2;
  bool read_only = 3;
}

message Container {
  string name = 1;
  string image = 2;
  string command = 3;
  ResourceClaim resource = 4;
  repeated BucketMount buckets = 5;
}

message CreatePodRequest {
  string name = 1;
  string tenant_id = 2;
  string workspace_id = 3;
  string project_id = 4;
  RestartPolicy.Type restart_policy = 5;

  repeated Container containers = 6;
  repeated Bucket buckets = 7;

  BilingMode.Type billing_mode = 8;
  string merchandise = 9;
}

message CreatePodReply {
  string pod_id = 1;
}

message CreatePodsRequest {
  repeated CreatePodRequest requests = 1;
}

message CreatePodsReply {
  // name -> pod_id
  map<string, string> pod_id_map = 1;
}

message QueryPodCountReply {
  uint64 count = 1;
}

message ResourcePool {
  enum Type {
    Unknown = 0;
    Reserved = 1;
    Dedicated = 2;
    Shared = 3;
  }
}

message CreateTenantRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  string tenant_id = 2 [(validate.rules).string.min_len = 1];

  // resource pool -> quota
  // pool: integer value of ResourcePool.Type
  map<int32, ResourceClaim> quotas = 4 [(validate.rules).map.keys.int32.lt = 4, (validate.rules).map.keys.int32.gt = 0];
}

message CreateTenantReply {
  // resource pool -> namespace
  // key is the integer value of ResourcePool.Type
  map<int32, string> namespaces = 1;
}

message RemoveTenantRequest {
  string tenant_id = 1 [(validate.rules).string.min_len = 1];
}

message CreateWorkspaceRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  string tenant_id = 2 [(validate.rules).string.min_len = 1];
  string workspace_id = 3 [(validate.rules).string.min_len = 1];

  // resource pool -> quota
  // key is the integer value of ResourcePool.Type
  map<int32, ResourceClaim> quotas = 5 [(validate.rules).map.keys.int32.lt = 4, (validate.rules).map.keys.int32.gt = 0];
}

message CreateWorkspaceReply {
  // resource pool -> namespace
  // key is the integer value of ResourcePool.Type
  map<int32, string> namespaces = 1;
}

message RemoveWorkspaceRequest {
  string tenant_id = 1 [(validate.rules).string.min_len = 1];
  string workspace_id = 2 [(validate.rules).string.min_len = 1];
}

message LabelNodeRequest {
  string node_name = 1;
  string label_key = 2;
  string label_value = 3;
}

message UnlabelNodeRequest {
  string node_name = 1;
  string label_key = 2;
}

message AllocateNodeToTenantRequest{
  string node_name = 1 [(validate.rules).string.min_len = 1];
  string tenant_id = 2 [(validate.rules).string.min_len = 1];
}

message AllocateNodeToWorkspaceRequest {
  string node_name = 1 [(validate.rules).string.min_len = 1];
  string workspace_id = 2 [(validate.rules).string.min_len = 1];
}

message AllocateNodeToResourcePoolRequest {
  string node_name = 1 [(validate.rules).string.min_len = 1];
  ResourcePool.Type pool = 2 [(validate.rules).enum = {not_in: [0, 1]}];
  string pool_id = 3 [(validate.rules).string.min_len = 1];
}

message ResourceAvailableRequest {
  ResourcePool.Type pool = 1 [(validate.rules).enum = {in: [2, 3]}];
  string spec_id = 2 [(validate.rules).string.min_len = 1];
  repeated SpecConfiguration configurations = 3;
}

message ResourceAvailableReply {
  int64 available = 1;
  string reason = 2;       // quota, pool
  repeated string key_resources = 3; // cpu/memory/teco/nvidia
  string detail = 4;
}
