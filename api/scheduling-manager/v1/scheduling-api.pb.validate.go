// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/scheduling-manager/v1/scheduling-api.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RestartPolicy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RestartPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestartPolicy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RestartPolicyMultiError, or
// nil if none found.
func (m *RestartPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *RestartPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RestartPolicyMultiError(errors)
	}

	return nil
}

// RestartPolicyMultiError is an error wrapping multiple validation errors
// returned by RestartPolicy.ValidateAll() if the designated constraints
// aren't met.
type RestartPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestartPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestartPolicyMultiError) AllErrors() []error { return m }

// RestartPolicyValidationError is the validation error returned by
// RestartPolicy.Validate if the designated constraints aren't met.
type RestartPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestartPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestartPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestartPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestartPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestartPolicyValidationError) ErrorName() string { return "RestartPolicyValidationError" }

// Error satisfies the builtin error interface
func (e RestartPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestartPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestartPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestartPolicyValidationError{}

// Validate checks the field values on ExtendedResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExtendedResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtendedResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtendedResourceMultiError, or nil if none found.
func (m *ExtendedResource) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtendedResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ExtendedResourceMultiError(errors)
	}

	return nil
}

// ExtendedResourceMultiError is an error wrapping multiple validation errors
// returned by ExtendedResource.ValidateAll() if the designated constraints
// aren't met.
type ExtendedResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtendedResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtendedResourceMultiError) AllErrors() []error { return m }

// ExtendedResourceValidationError is the validation error returned by
// ExtendedResource.Validate if the designated constraints aren't met.
type ExtendedResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtendedResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtendedResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtendedResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtendedResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtendedResourceValidationError) ErrorName() string { return "ExtendedResourceValidationError" }

// Error satisfies the builtin error interface
func (e ExtendedResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtendedResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtendedResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtendedResourceValidationError{}

// Validate checks the field values on Cpu with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Cpu) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cpu with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CpuMultiError, or nil if none found.
func (m *Cpu) ValidateAll() error {
	return m.validate(true)
}

func (m *Cpu) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Num

	// no validation rules for Architecture

	if len(errors) > 0 {
		return CpuMultiError(errors)
	}

	return nil
}

// CpuMultiError is an error wrapping multiple validation errors returned by
// Cpu.ValidateAll() if the designated constraints aren't met.
type CpuMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CpuMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CpuMultiError) AllErrors() []error { return m }

// CpuValidationError is the validation error returned by Cpu.Validate if the
// designated constraints aren't met.
type CpuValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CpuValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CpuValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CpuValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CpuValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CpuValidationError) ErrorName() string { return "CpuValidationError" }

// Error satisfies the builtin error interface
func (e CpuValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCpu.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CpuValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CpuValidationError{}

// Validate checks the field values on ResourceClaim with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResourceClaim) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceClaim with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourceClaimMultiError, or
// nil if none found.
func (m *ResourceClaim) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceClaim) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCpu()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourceClaimValidationError{
					field:  "Cpu",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourceClaimValidationError{
					field:  "Cpu",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCpu()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourceClaimValidationError{
				field:  "Cpu",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Memory

	{
		sorted_keys := make([]int32, len(m.GetExtendedResource()))
		i := 0
		for key := range m.GetExtendedResource() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetExtendedResource()[key]
			_ = val

			if val := key; val <= 0 || val >= 5 {
				err := ResourceClaimValidationError{
					field:  fmt.Sprintf("ExtendedResource[%v]", key),
					reason: "value must be inside range (0, 5)",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			// no validation rules for ExtendedResource[key]
		}
	}

	if len(errors) > 0 {
		return ResourceClaimMultiError(errors)
	}

	return nil
}

// ResourceClaimMultiError is an error wrapping multiple validation errors
// returned by ResourceClaim.ValidateAll() if the designated constraints
// aren't met.
type ResourceClaimMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceClaimMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceClaimMultiError) AllErrors() []error { return m }

// ResourceClaimValidationError is the validation error returned by
// ResourceClaim.Validate if the designated constraints aren't met.
type ResourceClaimValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceClaimValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceClaimValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceClaimValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceClaimValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceClaimValidationError) ErrorName() string { return "ResourceClaimValidationError" }

// Error satisfies the builtin error interface
func (e ResourceClaimValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceClaim.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceClaimValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceClaimValidationError{}

// Validate checks the field values on Bucket with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Bucket) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Bucket with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BucketMultiError, or nil if none found.
func (m *Bucket) ValidateAll() error {
	return m.validate(true)
}

func (m *Bucket) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Path

	if len(errors) > 0 {
		return BucketMultiError(errors)
	}

	return nil
}

// BucketMultiError is an error wrapping multiple validation errors returned by
// Bucket.ValidateAll() if the designated constraints aren't met.
type BucketMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BucketMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BucketMultiError) AllErrors() []error { return m }

// BucketValidationError is the validation error returned by Bucket.Validate if
// the designated constraints aren't met.
type BucketValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BucketValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BucketValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BucketValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BucketValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BucketValidationError) ErrorName() string { return "BucketValidationError" }

// Error satisfies the builtin error interface
func (e BucketValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBucket.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BucketValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BucketValidationError{}

// Validate checks the field values on BucketMount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BucketMount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BucketMount with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BucketMountMultiError, or
// nil if none found.
func (m *BucketMount) ValidateAll() error {
	return m.validate(true)
}

func (m *BucketMount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MntPath

	// no validation rules for ReadOnly

	if len(errors) > 0 {
		return BucketMountMultiError(errors)
	}

	return nil
}

// BucketMountMultiError is an error wrapping multiple validation errors
// returned by BucketMount.ValidateAll() if the designated constraints aren't met.
type BucketMountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BucketMountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BucketMountMultiError) AllErrors() []error { return m }

// BucketMountValidationError is the validation error returned by
// BucketMount.Validate if the designated constraints aren't met.
type BucketMountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BucketMountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BucketMountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BucketMountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BucketMountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BucketMountValidationError) ErrorName() string { return "BucketMountValidationError" }

// Error satisfies the builtin error interface
func (e BucketMountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBucketMount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BucketMountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BucketMountValidationError{}

// Validate checks the field values on Container with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Container) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Container with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContainerMultiError, or nil
// if none found.
func (m *Container) ValidateAll() error {
	return m.validate(true)
}

func (m *Container) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Image

	// no validation rules for Command

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContainerValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContainerValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContainerValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBuckets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ContainerValidationError{
						field:  fmt.Sprintf("Buckets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ContainerValidationError{
						field:  fmt.Sprintf("Buckets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ContainerValidationError{
					field:  fmt.Sprintf("Buckets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ContainerMultiError(errors)
	}

	return nil
}

// ContainerMultiError is an error wrapping multiple validation errors returned
// by Container.ValidateAll() if the designated constraints aren't met.
type ContainerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContainerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContainerMultiError) AllErrors() []error { return m }

// ContainerValidationError is the validation error returned by
// Container.Validate if the designated constraints aren't met.
type ContainerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContainerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContainerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContainerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContainerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContainerValidationError) ErrorName() string { return "ContainerValidationError" }

// Error satisfies the builtin error interface
func (e ContainerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContainer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContainerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContainerValidationError{}

// Validate checks the field values on CreatePodRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreatePodRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePodRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePodRequestMultiError, or nil if none found.
func (m *CreatePodRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePodRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for TenantId

	// no validation rules for WorkspaceId

	// no validation rules for ProjectId

	// no validation rules for RestartPolicy

	for idx, item := range m.GetContainers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreatePodRequestValidationError{
						field:  fmt.Sprintf("Containers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreatePodRequestValidationError{
						field:  fmt.Sprintf("Containers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreatePodRequestValidationError{
					field:  fmt.Sprintf("Containers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetBuckets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreatePodRequestValidationError{
						field:  fmt.Sprintf("Buckets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreatePodRequestValidationError{
						field:  fmt.Sprintf("Buckets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreatePodRequestValidationError{
					field:  fmt.Sprintf("Buckets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BillingMode

	// no validation rules for Merchandise

	if len(errors) > 0 {
		return CreatePodRequestMultiError(errors)
	}

	return nil
}

// CreatePodRequestMultiError is an error wrapping multiple validation errors
// returned by CreatePodRequest.ValidateAll() if the designated constraints
// aren't met.
type CreatePodRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePodRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePodRequestMultiError) AllErrors() []error { return m }

// CreatePodRequestValidationError is the validation error returned by
// CreatePodRequest.Validate if the designated constraints aren't met.
type CreatePodRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePodRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePodRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePodRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePodRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePodRequestValidationError) ErrorName() string { return "CreatePodRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreatePodRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePodRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePodRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePodRequestValidationError{}

// Validate checks the field values on CreatePodReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreatePodReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePodReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreatePodReplyMultiError,
// or nil if none found.
func (m *CreatePodReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePodReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PodId

	if len(errors) > 0 {
		return CreatePodReplyMultiError(errors)
	}

	return nil
}

// CreatePodReplyMultiError is an error wrapping multiple validation errors
// returned by CreatePodReply.ValidateAll() if the designated constraints
// aren't met.
type CreatePodReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePodReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePodReplyMultiError) AllErrors() []error { return m }

// CreatePodReplyValidationError is the validation error returned by
// CreatePodReply.Validate if the designated constraints aren't met.
type CreatePodReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePodReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePodReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePodReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePodReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePodReplyValidationError) ErrorName() string { return "CreatePodReplyValidationError" }

// Error satisfies the builtin error interface
func (e CreatePodReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePodReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePodReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePodReplyValidationError{}

// Validate checks the field values on CreatePodsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreatePodsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePodsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePodsRequestMultiError, or nil if none found.
func (m *CreatePodsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePodsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreatePodsRequestValidationError{
						field:  fmt.Sprintf("Requests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreatePodsRequestValidationError{
						field:  fmt.Sprintf("Requests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreatePodsRequestValidationError{
					field:  fmt.Sprintf("Requests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreatePodsRequestMultiError(errors)
	}

	return nil
}

// CreatePodsRequestMultiError is an error wrapping multiple validation errors
// returned by CreatePodsRequest.ValidateAll() if the designated constraints
// aren't met.
type CreatePodsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePodsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePodsRequestMultiError) AllErrors() []error { return m }

// CreatePodsRequestValidationError is the validation error returned by
// CreatePodsRequest.Validate if the designated constraints aren't met.
type CreatePodsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePodsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePodsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePodsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePodsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePodsRequestValidationError) ErrorName() string {
	return "CreatePodsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePodsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePodsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePodsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePodsRequestValidationError{}

// Validate checks the field values on CreatePodsReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreatePodsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePodsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePodsReplyMultiError, or nil if none found.
func (m *CreatePodsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePodsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PodIdMap

	if len(errors) > 0 {
		return CreatePodsReplyMultiError(errors)
	}

	return nil
}

// CreatePodsReplyMultiError is an error wrapping multiple validation errors
// returned by CreatePodsReply.ValidateAll() if the designated constraints
// aren't met.
type CreatePodsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePodsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePodsReplyMultiError) AllErrors() []error { return m }

// CreatePodsReplyValidationError is the validation error returned by
// CreatePodsReply.Validate if the designated constraints aren't met.
type CreatePodsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePodsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePodsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePodsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePodsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePodsReplyValidationError) ErrorName() string { return "CreatePodsReplyValidationError" }

// Error satisfies the builtin error interface
func (e CreatePodsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePodsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePodsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePodsReplyValidationError{}

// Validate checks the field values on QueryPodCountReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryPodCountReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryPodCountReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryPodCountReplyMultiError, or nil if none found.
func (m *QueryPodCountReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryPodCountReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return QueryPodCountReplyMultiError(errors)
	}

	return nil
}

// QueryPodCountReplyMultiError is an error wrapping multiple validation errors
// returned by QueryPodCountReply.ValidateAll() if the designated constraints
// aren't met.
type QueryPodCountReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryPodCountReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryPodCountReplyMultiError) AllErrors() []error { return m }

// QueryPodCountReplyValidationError is the validation error returned by
// QueryPodCountReply.Validate if the designated constraints aren't met.
type QueryPodCountReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryPodCountReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryPodCountReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryPodCountReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryPodCountReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryPodCountReplyValidationError) ErrorName() string {
	return "QueryPodCountReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryPodCountReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryPodCountReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryPodCountReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryPodCountReplyValidationError{}

// Validate checks the field values on ResourcePool with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResourcePool) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourcePool with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourcePoolMultiError, or
// nil if none found.
func (m *ResourcePool) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourcePool) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ResourcePoolMultiError(errors)
	}

	return nil
}

// ResourcePoolMultiError is an error wrapping multiple validation errors
// returned by ResourcePool.ValidateAll() if the designated constraints aren't met.
type ResourcePoolMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourcePoolMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourcePoolMultiError) AllErrors() []error { return m }

// ResourcePoolValidationError is the validation error returned by
// ResourcePool.Validate if the designated constraints aren't met.
type ResourcePoolValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourcePoolValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourcePoolValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourcePoolValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourcePoolValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourcePoolValidationError) ErrorName() string { return "ResourcePoolValidationError" }

// Error satisfies the builtin error interface
func (e ResourcePoolValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourcePool.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourcePoolValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourcePoolValidationError{}

// Validate checks the field values on CreateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTenantRequestMultiError, or nil if none found.
func (m *CreateTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := CreateTenantRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTenantId()) < 1 {
		err := CreateTenantRequestValidationError{
			field:  "TenantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]int32, len(m.GetQuotas()))
		i := 0
		for key := range m.GetQuotas() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetQuotas()[key]
			_ = val

			if val := key; val <= 0 || val >= 4 {
				err := CreateTenantRequestValidationError{
					field:  fmt.Sprintf("Quotas[%v]", key),
					reason: "value must be inside range (0, 4)",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CreateTenantRequestValidationError{
							field:  fmt.Sprintf("Quotas[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CreateTenantRequestValidationError{
							field:  fmt.Sprintf("Quotas[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CreateTenantRequestValidationError{
						field:  fmt.Sprintf("Quotas[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CreateTenantRequestMultiError(errors)
	}

	return nil
}

// CreateTenantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTenantRequestMultiError) AllErrors() []error { return m }

// CreateTenantRequestValidationError is the validation error returned by
// CreateTenantRequest.Validate if the designated constraints aren't met.
type CreateTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTenantRequestValidationError) ErrorName() string {
	return "CreateTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTenantRequestValidationError{}

// Validate checks the field values on CreateTenantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateTenantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTenantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTenantReplyMultiError, or nil if none found.
func (m *CreateTenantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTenantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Namespaces

	if len(errors) > 0 {
		return CreateTenantReplyMultiError(errors)
	}

	return nil
}

// CreateTenantReplyMultiError is an error wrapping multiple validation errors
// returned by CreateTenantReply.ValidateAll() if the designated constraints
// aren't met.
type CreateTenantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTenantReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTenantReplyMultiError) AllErrors() []error { return m }

// CreateTenantReplyValidationError is the validation error returned by
// CreateTenantReply.Validate if the designated constraints aren't met.
type CreateTenantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTenantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTenantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTenantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTenantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTenantReplyValidationError) ErrorName() string {
	return "CreateTenantReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTenantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTenantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTenantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTenantReplyValidationError{}

// Validate checks the field values on RemoveTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveTenantRequestMultiError, or nil if none found.
func (m *RemoveTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTenantId()) < 1 {
		err := RemoveTenantRequestValidationError{
			field:  "TenantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RemoveTenantRequestMultiError(errors)
	}

	return nil
}

// RemoveTenantRequestMultiError is an error wrapping multiple validation
// errors returned by RemoveTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type RemoveTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveTenantRequestMultiError) AllErrors() []error { return m }

// RemoveTenantRequestValidationError is the validation error returned by
// RemoveTenantRequest.Validate if the designated constraints aren't met.
type RemoveTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveTenantRequestValidationError) ErrorName() string {
	return "RemoveTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveTenantRequestValidationError{}

// Validate checks the field values on CreateWorkspaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateWorkspaceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateWorkspaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateWorkspaceRequestMultiError, or nil if none found.
func (m *CreateWorkspaceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateWorkspaceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := CreateWorkspaceRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTenantId()) < 1 {
		err := CreateWorkspaceRequestValidationError{
			field:  "TenantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetWorkspaceId()) < 1 {
		err := CreateWorkspaceRequestValidationError{
			field:  "WorkspaceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]int32, len(m.GetQuotas()))
		i := 0
		for key := range m.GetQuotas() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetQuotas()[key]
			_ = val

			if val := key; val <= 0 || val >= 4 {
				err := CreateWorkspaceRequestValidationError{
					field:  fmt.Sprintf("Quotas[%v]", key),
					reason: "value must be inside range (0, 4)",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CreateWorkspaceRequestValidationError{
							field:  fmt.Sprintf("Quotas[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CreateWorkspaceRequestValidationError{
							field:  fmt.Sprintf("Quotas[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CreateWorkspaceRequestValidationError{
						field:  fmt.Sprintf("Quotas[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CreateWorkspaceRequestMultiError(errors)
	}

	return nil
}

// CreateWorkspaceRequestMultiError is an error wrapping multiple validation
// errors returned by CreateWorkspaceRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateWorkspaceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateWorkspaceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateWorkspaceRequestMultiError) AllErrors() []error { return m }

// CreateWorkspaceRequestValidationError is the validation error returned by
// CreateWorkspaceRequest.Validate if the designated constraints aren't met.
type CreateWorkspaceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateWorkspaceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateWorkspaceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateWorkspaceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateWorkspaceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateWorkspaceRequestValidationError) ErrorName() string {
	return "CreateWorkspaceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateWorkspaceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateWorkspaceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateWorkspaceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateWorkspaceRequestValidationError{}

// Validate checks the field values on CreateWorkspaceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateWorkspaceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateWorkspaceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateWorkspaceReplyMultiError, or nil if none found.
func (m *CreateWorkspaceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateWorkspaceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Namespaces

	if len(errors) > 0 {
		return CreateWorkspaceReplyMultiError(errors)
	}

	return nil
}

// CreateWorkspaceReplyMultiError is an error wrapping multiple validation
// errors returned by CreateWorkspaceReply.ValidateAll() if the designated
// constraints aren't met.
type CreateWorkspaceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateWorkspaceReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateWorkspaceReplyMultiError) AllErrors() []error { return m }

// CreateWorkspaceReplyValidationError is the validation error returned by
// CreateWorkspaceReply.Validate if the designated constraints aren't met.
type CreateWorkspaceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateWorkspaceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateWorkspaceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateWorkspaceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateWorkspaceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateWorkspaceReplyValidationError) ErrorName() string {
	return "CreateWorkspaceReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateWorkspaceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateWorkspaceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateWorkspaceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateWorkspaceReplyValidationError{}

// Validate checks the field values on RemoveWorkspaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveWorkspaceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveWorkspaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveWorkspaceRequestMultiError, or nil if none found.
func (m *RemoveWorkspaceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveWorkspaceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTenantId()) < 1 {
		err := RemoveWorkspaceRequestValidationError{
			field:  "TenantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetWorkspaceId()) < 1 {
		err := RemoveWorkspaceRequestValidationError{
			field:  "WorkspaceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RemoveWorkspaceRequestMultiError(errors)
	}

	return nil
}

// RemoveWorkspaceRequestMultiError is an error wrapping multiple validation
// errors returned by RemoveWorkspaceRequest.ValidateAll() if the designated
// constraints aren't met.
type RemoveWorkspaceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveWorkspaceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveWorkspaceRequestMultiError) AllErrors() []error { return m }

// RemoveWorkspaceRequestValidationError is the validation error returned by
// RemoveWorkspaceRequest.Validate if the designated constraints aren't met.
type RemoveWorkspaceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveWorkspaceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveWorkspaceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveWorkspaceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveWorkspaceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveWorkspaceRequestValidationError) ErrorName() string {
	return "RemoveWorkspaceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveWorkspaceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveWorkspaceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveWorkspaceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveWorkspaceRequestValidationError{}

// Validate checks the field values on LabelNodeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LabelNodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LabelNodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LabelNodeRequestMultiError, or nil if none found.
func (m *LabelNodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LabelNodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NodeName

	// no validation rules for LabelKey

	// no validation rules for LabelValue

	if len(errors) > 0 {
		return LabelNodeRequestMultiError(errors)
	}

	return nil
}

// LabelNodeRequestMultiError is an error wrapping multiple validation errors
// returned by LabelNodeRequest.ValidateAll() if the designated constraints
// aren't met.
type LabelNodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LabelNodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LabelNodeRequestMultiError) AllErrors() []error { return m }

// LabelNodeRequestValidationError is the validation error returned by
// LabelNodeRequest.Validate if the designated constraints aren't met.
type LabelNodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LabelNodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LabelNodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LabelNodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LabelNodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LabelNodeRequestValidationError) ErrorName() string { return "LabelNodeRequestValidationError" }

// Error satisfies the builtin error interface
func (e LabelNodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabelNodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LabelNodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LabelNodeRequestValidationError{}

// Validate checks the field values on UnlabelNodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnlabelNodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnlabelNodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnlabelNodeRequestMultiError, or nil if none found.
func (m *UnlabelNodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnlabelNodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NodeName

	// no validation rules for LabelKey

	if len(errors) > 0 {
		return UnlabelNodeRequestMultiError(errors)
	}

	return nil
}

// UnlabelNodeRequestMultiError is an error wrapping multiple validation errors
// returned by UnlabelNodeRequest.ValidateAll() if the designated constraints
// aren't met.
type UnlabelNodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnlabelNodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnlabelNodeRequestMultiError) AllErrors() []error { return m }

// UnlabelNodeRequestValidationError is the validation error returned by
// UnlabelNodeRequest.Validate if the designated constraints aren't met.
type UnlabelNodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnlabelNodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnlabelNodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnlabelNodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnlabelNodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnlabelNodeRequestValidationError) ErrorName() string {
	return "UnlabelNodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnlabelNodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnlabelNodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnlabelNodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnlabelNodeRequestValidationError{}

// Validate checks the field values on AllocateNodeToTenantRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllocateNodeToTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllocateNodeToTenantRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllocateNodeToTenantRequestMultiError, or nil if none found.
func (m *AllocateNodeToTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AllocateNodeToTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetNodeName()) < 1 {
		err := AllocateNodeToTenantRequestValidationError{
			field:  "NodeName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTenantId()) < 1 {
		err := AllocateNodeToTenantRequestValidationError{
			field:  "TenantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AllocateNodeToTenantRequestMultiError(errors)
	}

	return nil
}

// AllocateNodeToTenantRequestMultiError is an error wrapping multiple
// validation errors returned by AllocateNodeToTenantRequest.ValidateAll() if
// the designated constraints aren't met.
type AllocateNodeToTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllocateNodeToTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllocateNodeToTenantRequestMultiError) AllErrors() []error { return m }

// AllocateNodeToTenantRequestValidationError is the validation error returned
// by AllocateNodeToTenantRequest.Validate if the designated constraints
// aren't met.
type AllocateNodeToTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllocateNodeToTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllocateNodeToTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllocateNodeToTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllocateNodeToTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllocateNodeToTenantRequestValidationError) ErrorName() string {
	return "AllocateNodeToTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AllocateNodeToTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllocateNodeToTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllocateNodeToTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllocateNodeToTenantRequestValidationError{}

// Validate checks the field values on AllocateNodeToWorkspaceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllocateNodeToWorkspaceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllocateNodeToWorkspaceRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AllocateNodeToWorkspaceRequestMultiError, or nil if none found.
func (m *AllocateNodeToWorkspaceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AllocateNodeToWorkspaceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetNodeName()) < 1 {
		err := AllocateNodeToWorkspaceRequestValidationError{
			field:  "NodeName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetWorkspaceId()) < 1 {
		err := AllocateNodeToWorkspaceRequestValidationError{
			field:  "WorkspaceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AllocateNodeToWorkspaceRequestMultiError(errors)
	}

	return nil
}

// AllocateNodeToWorkspaceRequestMultiError is an error wrapping multiple
// validation errors returned by AllocateNodeToWorkspaceRequest.ValidateAll()
// if the designated constraints aren't met.
type AllocateNodeToWorkspaceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllocateNodeToWorkspaceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllocateNodeToWorkspaceRequestMultiError) AllErrors() []error { return m }

// AllocateNodeToWorkspaceRequestValidationError is the validation error
// returned by AllocateNodeToWorkspaceRequest.Validate if the designated
// constraints aren't met.
type AllocateNodeToWorkspaceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllocateNodeToWorkspaceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllocateNodeToWorkspaceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllocateNodeToWorkspaceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllocateNodeToWorkspaceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllocateNodeToWorkspaceRequestValidationError) ErrorName() string {
	return "AllocateNodeToWorkspaceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AllocateNodeToWorkspaceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllocateNodeToWorkspaceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllocateNodeToWorkspaceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllocateNodeToWorkspaceRequestValidationError{}

// Validate checks the field values on AllocateNodeToResourcePoolRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AllocateNodeToResourcePoolRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllocateNodeToResourcePoolRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AllocateNodeToResourcePoolRequestMultiError, or nil if none found.
func (m *AllocateNodeToResourcePoolRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AllocateNodeToResourcePoolRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetNodeName()) < 1 {
		err := AllocateNodeToResourcePoolRequestValidationError{
			field:  "NodeName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _AllocateNodeToResourcePoolRequest_Pool_NotInLookup[m.GetPool()]; ok {
		err := AllocateNodeToResourcePoolRequestValidationError{
			field:  "Pool",
			reason: "value must not be in list [Unknown Reserved]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPoolId()) < 1 {
		err := AllocateNodeToResourcePoolRequestValidationError{
			field:  "PoolId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AllocateNodeToResourcePoolRequestMultiError(errors)
	}

	return nil
}

// AllocateNodeToResourcePoolRequestMultiError is an error wrapping multiple
// validation errors returned by
// AllocateNodeToResourcePoolRequest.ValidateAll() if the designated
// constraints aren't met.
type AllocateNodeToResourcePoolRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllocateNodeToResourcePoolRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllocateNodeToResourcePoolRequestMultiError) AllErrors() []error { return m }

// AllocateNodeToResourcePoolRequestValidationError is the validation error
// returned by AllocateNodeToResourcePoolRequest.Validate if the designated
// constraints aren't met.
type AllocateNodeToResourcePoolRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllocateNodeToResourcePoolRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllocateNodeToResourcePoolRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllocateNodeToResourcePoolRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllocateNodeToResourcePoolRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllocateNodeToResourcePoolRequestValidationError) ErrorName() string {
	return "AllocateNodeToResourcePoolRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AllocateNodeToResourcePoolRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllocateNodeToResourcePoolRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllocateNodeToResourcePoolRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllocateNodeToResourcePoolRequestValidationError{}

var _AllocateNodeToResourcePoolRequest_Pool_NotInLookup = map[ResourcePool_Type]struct{}{
	0: {},
	1: {},
}

// Validate checks the field values on ResourceAvailableRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResourceAvailableRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceAvailableRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceAvailableRequestMultiError, or nil if none found.
func (m *ResourceAvailableRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceAvailableRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _ResourceAvailableRequest_Pool_InLookup[m.GetPool()]; !ok {
		err := ResourceAvailableRequestValidationError{
			field:  "Pool",
			reason: "value must be in list [Dedicated Shared]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSpecId()) < 1 {
		err := ResourceAvailableRequestValidationError{
			field:  "SpecId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetConfigurations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResourceAvailableRequestValidationError{
						field:  fmt.Sprintf("Configurations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResourceAvailableRequestValidationError{
						field:  fmt.Sprintf("Configurations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResourceAvailableRequestValidationError{
					field:  fmt.Sprintf("Configurations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResourceAvailableRequestMultiError(errors)
	}

	return nil
}

// ResourceAvailableRequestMultiError is an error wrapping multiple validation
// errors returned by ResourceAvailableRequest.ValidateAll() if the designated
// constraints aren't met.
type ResourceAvailableRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceAvailableRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceAvailableRequestMultiError) AllErrors() []error { return m }

// ResourceAvailableRequestValidationError is the validation error returned by
// ResourceAvailableRequest.Validate if the designated constraints aren't met.
type ResourceAvailableRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceAvailableRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceAvailableRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceAvailableRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceAvailableRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceAvailableRequestValidationError) ErrorName() string {
	return "ResourceAvailableRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResourceAvailableRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceAvailableRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceAvailableRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceAvailableRequestValidationError{}

var _ResourceAvailableRequest_Pool_InLookup = map[ResourcePool_Type]struct{}{
	2: {},
	3: {},
}

// Validate checks the field values on ResourceAvailableReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResourceAvailableReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceAvailableReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceAvailableReplyMultiError, or nil if none found.
func (m *ResourceAvailableReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceAvailableReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Available

	// no validation rules for Reason

	// no validation rules for Detail

	if len(errors) > 0 {
		return ResourceAvailableReplyMultiError(errors)
	}

	return nil
}

// ResourceAvailableReplyMultiError is an error wrapping multiple validation
// errors returned by ResourceAvailableReply.ValidateAll() if the designated
// constraints aren't met.
type ResourceAvailableReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceAvailableReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceAvailableReplyMultiError) AllErrors() []error { return m }

// ResourceAvailableReplyValidationError is the validation error returned by
// ResourceAvailableReply.Validate if the designated constraints aren't met.
type ResourceAvailableReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceAvailableReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceAvailableReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceAvailableReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceAvailableReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceAvailableReplyValidationError) ErrorName() string {
	return "ResourceAvailableReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ResourceAvailableReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceAvailableReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceAvailableReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceAvailableReplyValidationError{}
