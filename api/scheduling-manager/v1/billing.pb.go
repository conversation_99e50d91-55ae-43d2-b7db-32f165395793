// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.12.4
// source: api/scheduling-manager/v1/billing.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProductType int32

const (
	// PRODUCT_COMPUTE: 计算
	ProductType_PRODUCT_COMPUTE ProductType = 0
	// PRODUCT_STORAGE: 存储
	ProductType_PRODUCT_STORAGE ProductType = 1
	// PRODUCT_NETWORK: 网络
	ProductType_PRODUCT_NETWORK ProductType = 2
)

// Enum value maps for ProductType.
var (
	ProductType_name = map[int32]string{
		0: "PRODUCT_COMPUTE",
		1: "PRODUCT_STORAGE",
		2: "PRODUCT_NETWORK",
	}
	ProductType_value = map[string]int32{
		"PRODUCT_COMPUTE": 0,
		"PRODUCT_STORAGE": 1,
		"PRODUCT_NETWORK": 2,
	}
)

func (x ProductType) Enum() *ProductType {
	p := new(ProductType)
	*p = x
	return p
}

func (x ProductType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_scheduling_manager_v1_billing_proto_enumTypes[0].Descriptor()
}

func (ProductType) Type() protoreflect.EnumType {
	return &file_api_scheduling_manager_v1_billing_proto_enumTypes[0]
}

func (x ProductType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductType.Descriptor instead.
func (ProductType) EnumDescriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{0}
}

type ValueType int32

const (
	// VALUE_TYPE_FIXED: 固定值
	ValueType_VALUE_TYPE_FIXED ValueType = 0
	// VALUE_TYPE_LIST: 列表值
	ValueType_VALUE_TYPE_LIST ValueType = 1
	// VALUE_TYPE_RANGE: 范围值
	ValueType_VALUE_TYPE_RANGE ValueType = 2
)

// Enum value maps for ValueType.
var (
	ValueType_name = map[int32]string{
		0: "VALUE_TYPE_FIXED",
		1: "VALUE_TYPE_LIST",
		2: "VALUE_TYPE_RANGE",
	}
	ValueType_value = map[string]int32{
		"VALUE_TYPE_FIXED": 0,
		"VALUE_TYPE_LIST":  1,
		"VALUE_TYPE_RANGE": 2,
	}
)

func (x ValueType) Enum() *ValueType {
	p := new(ValueType)
	*p = x
	return p
}

func (x ValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_scheduling_manager_v1_billing_proto_enumTypes[1].Descriptor()
}

func (ValueType) Type() protoreflect.EnumType {
	return &file_api_scheduling_manager_v1_billing_proto_enumTypes[1]
}

func (x ValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValueType.Descriptor instead.
func (ValueType) EnumDescriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{1}
}

type BilingMode_Type int32

const (
	BilingMode_UNKNOWN  BilingMode_Type = 0
	BilingMode_QUANTITY BilingMode_Type = 1
	BilingMode_WHOLE    BilingMode_Type = 2
	BilingMode_RESERVED BilingMode_Type = 3
)

// Enum value maps for BilingMode_Type.
var (
	BilingMode_Type_name = map[int32]string{
		0: "UNKNOWN",
		1: "QUANTITY",
		2: "WHOLE",
		3: "RESERVED",
	}
	BilingMode_Type_value = map[string]int32{
		"UNKNOWN":  0,
		"QUANTITY": 1,
		"WHOLE":    2,
		"RESERVED": 3,
	}
)

func (x BilingMode_Type) Enum() *BilingMode_Type {
	p := new(BilingMode_Type)
	*p = x
	return p
}

func (x BilingMode_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BilingMode_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_scheduling_manager_v1_billing_proto_enumTypes[2].Descriptor()
}

func (BilingMode_Type) Type() protoreflect.EnumType {
	return &file_api_scheduling_manager_v1_billing_proto_enumTypes[2]
}

func (x BilingMode_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BilingMode_Type.Descriptor instead.
func (BilingMode_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{0, 0}
}

type BilingMode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BilingMode) Reset() {
	*x = BilingMode{}
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BilingMode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BilingMode) ProtoMessage() {}

func (x *BilingMode) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BilingMode.ProtoReflect.Descriptor instead.
func (*BilingMode) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{0}
}

type Form struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Form          string                 `protobuf:"bytes,2,opt,name=form,proto3" json:"form,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Form) Reset() {
	*x = Form{}
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Form) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Form) ProtoMessage() {}

func (x *Form) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Form.ProtoReflect.Descriptor instead.
func (*Form) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{1}
}

func (x *Form) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Form) GetForm() string {
	if x != nil {
		return x.Form
	}
	return ""
}

type Configuration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Unit          []string               `protobuf:"bytes,3,rep,name=unit,proto3" json:"unit,omitempty"`
	Type          ProductType            `protobuf:"varint,4,opt,name=type,proto3,enum=api.scheduling_manager.v1.ProductType" json:"type,omitempty"`
	Category      string                 `protobuf:"bytes,5,opt,name=category,proto3" json:"category,omitempty"`
	ProductForm   []*Form                `protobuf:"bytes,6,rep,name=product_form,json=productForm,proto3" json:"product_form,omitempty"`
	Capacity      int64                  `protobuf:"varint,7,opt,name=capacity,proto3" json:"capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Configuration) Reset() {
	*x = Configuration{}
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Configuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Configuration) ProtoMessage() {}

func (x *Configuration) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Configuration.ProtoReflect.Descriptor instead.
func (*Configuration) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{2}
}

func (x *Configuration) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Configuration) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Configuration) GetUnit() []string {
	if x != nil {
		return x.Unit
	}
	return nil
}

func (x *Configuration) GetType() ProductType {
	if x != nil {
		return x.Type
	}
	return ProductType_PRODUCT_COMPUTE
}

func (x *Configuration) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *Configuration) GetProductForm() []*Form {
	if x != nil {
		return x.ProductForm
	}
	return nil
}

func (x *Configuration) GetCapacity() int64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

type SpecConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type          ProductType            `protobuf:"varint,3,opt,name=type,proto3,enum=api.scheduling_manager.v1.ProductType" json:"type,omitempty"`
	Unit          string                 `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	Enable        bool                   `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	ValueType     ValueType              `protobuf:"varint,6,opt,name=valueType,proto3,enum=api.scheduling_manager.v1.ValueType" json:"valueType,omitempty"`
	Value         string                 `protobuf:"bytes,7,opt,name=value,proto3" json:"value,omitempty"`
	Configuration *Configuration         `protobuf:"bytes,8,opt,name=configuration,proto3" json:"configuration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpecConfiguration) Reset() {
	*x = SpecConfiguration{}
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpecConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecConfiguration) ProtoMessage() {}

func (x *SpecConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_api_scheduling_manager_v1_billing_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecConfiguration.ProtoReflect.Descriptor instead.
func (*SpecConfiguration) Descriptor() ([]byte, []int) {
	return file_api_scheduling_manager_v1_billing_proto_rawDescGZIP(), []int{3}
}

func (x *SpecConfiguration) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SpecConfiguration) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SpecConfiguration) GetType() ProductType {
	if x != nil {
		return x.Type
	}
	return ProductType_PRODUCT_COMPUTE
}

func (x *SpecConfiguration) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *SpecConfiguration) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SpecConfiguration) GetValueType() ValueType {
	if x != nil {
		return x.ValueType
	}
	return ValueType_VALUE_TYPE_FIXED
}

func (x *SpecConfiguration) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *SpecConfiguration) GetConfiguration() *Configuration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

var File_api_scheduling_manager_v1_billing_proto protoreflect.FileDescriptor

var file_api_scheduling_manager_v1_billing_proto_rawDesc = string([]byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x22, 0x48, 0x0a, 0x0a, 0x42, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x22, 0x3a, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x51, 0x55, 0x41, 0x4e, 0x54,
	0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x57, 0x48, 0x4f, 0x4c, 0x45, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x10, 0x03, 0x22, 0x2a,
	0x0a, 0x04, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xff, 0x01, 0x0a, 0x0d, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x6e, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x6f, 0x72, 0x6d, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0xc9, 0x02, 0x0a,
	0x11, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x42,
	0x0a, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4e, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x4c, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x4b, 0x10, 0x02, 0x2a, 0x4c, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x41, 0x4c,
	0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x41, 0x4e,
	0x47, 0x45, 0x10, 0x02, 0x42, 0x60, 0x0a, 0x24, 0x64, 0x65, 0x76, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x42, 0x18, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_api_scheduling_manager_v1_billing_proto_rawDescOnce sync.Once
	file_api_scheduling_manager_v1_billing_proto_rawDescData []byte
)

func file_api_scheduling_manager_v1_billing_proto_rawDescGZIP() []byte {
	file_api_scheduling_manager_v1_billing_proto_rawDescOnce.Do(func() {
		file_api_scheduling_manager_v1_billing_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_scheduling_manager_v1_billing_proto_rawDesc), len(file_api_scheduling_manager_v1_billing_proto_rawDesc)))
	})
	return file_api_scheduling_manager_v1_billing_proto_rawDescData
}

var file_api_scheduling_manager_v1_billing_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_scheduling_manager_v1_billing_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_scheduling_manager_v1_billing_proto_goTypes = []any{
	(ProductType)(0),          // 0: api.scheduling_manager.v1.ProductType
	(ValueType)(0),            // 1: api.scheduling_manager.v1.ValueType
	(BilingMode_Type)(0),      // 2: api.scheduling_manager.v1.BilingMode.Type
	(*BilingMode)(nil),        // 3: api.scheduling_manager.v1.BilingMode
	(*Form)(nil),              // 4: api.scheduling_manager.v1.Form
	(*Configuration)(nil),     // 5: api.scheduling_manager.v1.Configuration
	(*SpecConfiguration)(nil), // 6: api.scheduling_manager.v1.SpecConfiguration
}
var file_api_scheduling_manager_v1_billing_proto_depIdxs = []int32{
	0, // 0: api.scheduling_manager.v1.Configuration.type:type_name -> api.scheduling_manager.v1.ProductType
	4, // 1: api.scheduling_manager.v1.Configuration.product_form:type_name -> api.scheduling_manager.v1.Form
	0, // 2: api.scheduling_manager.v1.SpecConfiguration.type:type_name -> api.scheduling_manager.v1.ProductType
	1, // 3: api.scheduling_manager.v1.SpecConfiguration.valueType:type_name -> api.scheduling_manager.v1.ValueType
	5, // 4: api.scheduling_manager.v1.SpecConfiguration.configuration:type_name -> api.scheduling_manager.v1.Configuration
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_scheduling_manager_v1_billing_proto_init() }
func file_api_scheduling_manager_v1_billing_proto_init() {
	if File_api_scheduling_manager_v1_billing_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_scheduling_manager_v1_billing_proto_rawDesc), len(file_api_scheduling_manager_v1_billing_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_scheduling_manager_v1_billing_proto_goTypes,
		DependencyIndexes: file_api_scheduling_manager_v1_billing_proto_depIdxs,
		EnumInfos:         file_api_scheduling_manager_v1_billing_proto_enumTypes,
		MessageInfos:      file_api_scheduling_manager_v1_billing_proto_msgTypes,
	}.Build()
	File_api_scheduling_manager_v1_billing_proto = out.File
	file_api_scheduling_manager_v1_billing_proto_goTypes = nil
	file_api_scheduling_manager_v1_billing_proto_depIdxs = nil
}
