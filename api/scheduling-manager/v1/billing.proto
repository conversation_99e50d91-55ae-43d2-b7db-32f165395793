syntax = "proto3";

package api.scheduling_manager.v1;

option go_package = "api/scheduling-manager/v1;v1";
option java_multiple_files = true;
option java_package = "dev.kratos.api.scheduling-manager.v1";
option java_outer_classname = "SchedulingManagerProtoV1";


message BilingMode {
  enum Type {
    UNKNOWN = 0;
    QUANTITY = 1;
    WHOLE = 2;
    RESERVED = 3;
  }
}

enum ProductType{
  //PRODUCT_COMPUTE: 计算
  PRODUCT_COMPUTE = 0;
  //PRODUCT_STORAGE: 存储
  PRODUCT_STORAGE = 1;
  //PRODUCT_NETWORK: 网络
  PRODUCT_NETWORK = 2;
}
  
enum ValueType{
  //VALUE_TYPE_FIXED: 固定值
  VALUE_TYPE_FIXED= 0;
  //VALUE_TYPE_LIST: 列表值
  VALUE_TYPE_LIST=1;
  //VALUE_TYPE_RANGE: 范围值
  VALUE_TYPE_RANGE=2;
}

message Form{
  int32 id = 1;
  string form = 2;
}
  
message Configuration{
  string id = 1;
  string name = 2;
  repeated string unit = 3;
  ProductType type = 4;
  string category = 5;
  repeated Form product_form = 6;
  int64 capacity = 7;
}

message SpecConfiguration{
  string id =1;
  string name = 2;
  ProductType type=3;
  string unit =4;
  bool enable = 5;
  ValueType valueType=6;
  string value = 7;
  Configuration configuration = 8;
}