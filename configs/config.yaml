server:
  http:
    addr: 0.0.0.0:8000
    timeout: 120s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 120s
data:
  database:
    driver: mysql
    source: root:root@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
  nats:
    url: nats://*********:30866
  redis:
    addr: 127.0.0.1:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
k8sConfig:
  outCluster: true
  devEnv: true
  devConfig:
    host: https://*********:6443
    bearerToken: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  QPS: 100
  Burst: 100
tenant:
  server:
    http:
      addr: *********:32714
    grpc:
      addr: *********:30772
harbor:
  url: https://business1.tecorigin.io:5443
  username: admin
  password: Teco@135
  secretName: harbor-image-pull-secret
  insecureRegistry: true
