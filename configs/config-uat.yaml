server:
  http:
    addr: 0.0.0.0:8000
    timeout: 120s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 120s
data:
  database:
    driver: mysql
    source: root:root@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
  nats:
    url: nats://*********:32422
    # url: nats://nats.nats:4222
  redis:
    addr: 127.0.0.1:11379
    read_timeout: 0.2s
    write_timeout: 0.2s
k8sConfig:
  outCluster: true
  devEnv: true
  devConfig:
    host: https://*********:6443
    # ********* admin-user-token-s7zvw
    bearerToken: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  QPS: 100
  Burst: 100
tenant:
  server:
    http:
      addr: *********:30826
    grpc:
      addr: *********:32124
harbor:
  url: https://business2.tecorigin.io:5443
  username: admin
  password: Teco@135
  secretName: harbor-image-pull-secret
  insecureRegistry: true
