module git-plat.tecorigin.net/ai-platform/scheduling-manager

go 1.23

require (
	git-plat.tecorigin.net/ai-platform/backend-lib v0.0.0-20250324033509-3a2edec32f0d
	git-plat.tecorigin.net/ai-platform/billing-server v1.7.0
	git-plat.tecorigin.net/ai-platform/console-backend v1.7.0-hotfix.0.20250417091222-e97880c43d30
	git-plat.tecorigin.net/ai-platform/errno v0.0.0-20250324091719-1bb491693361
	git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc v0.0.0-20241018120258-15da64316253
	github.com/envoyproxy/protoc-gen-validate v1.1.0
	github.com/go-kratos/kratos/v2 v2.8.0
	github.com/google/wire v0.6.0
	github.com/grpc-ecosystem/go-grpc-middleware v1.4.0
	github.com/grpc-ecosystem/go-grpc-middleware/providers/openmetrics/v2 v2.0.0-rc.3
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.22.0
	github.com/imdario/mergo v0.3.16
	github.com/justinas/alice v1.2.0
	github.com/nats-io/nats.go v1.37.0
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.20.5
	github.com/robfig/cron/v3 v3.0.1
	github.com/slok/go-http-metrics v0.13.0
	github.com/spf13/cast v1.7.1
	go.uber.org/automaxprocs v1.6.0
	go.uber.org/zap v1.27.0
	gomodules.xyz/jsonpatch/v2 v2.4.0
	google.golang.org/genproto/googleapis/api v0.0.0-20240924160255-9d4c2d233b61
	google.golang.org/grpc v1.67.0
	google.golang.org/protobuf v1.34.2
	k8s.io/api v0.31.0
	k8s.io/apimachinery v0.31.1
	k8s.io/apiserver v0.31.0
	k8s.io/client-go v0.31.0
	k8s.io/component-helpers v0.27.2
	k8s.io/klog/v2 v2.130.1
	k8s.io/kubernetes v1.27.2
	stathat.com/c/consistent v1.0.0
	volcano.sh/apis v1.8.2
	volcano.sh/volcano v1.8.2
)

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/blang/semver/v4 v4.0.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dlclark/regexp2 v1.11.4 // indirect
	github.com/elastic/go-elasticsearch/v7 v7.17.10 // indirect
	github.com/emicklei/go-restful/v3 v3.12.1 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-kratos/aegis v0.2.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/jsonreference v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/gojek/heimdall/v7 v7.0.3 // indirect
	github.com/gojek/valkyrie v0.0.0-20190210220504-8f62c1e7ba45 // indirect
	github.com/golang-jwt/jwt/v4 v4.5.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/cadvisor v0.47.1 // indirect
	github.com/google/gnostic v0.5.7-v3refs // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/gofuzz v1.2.1-0.20210504230335-f78f29fc09ea // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware/v2 v2.0.0-rc.2.0.20210128111500-3ff779b52992 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.10 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/moby/sys/mountinfo v0.6.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/nats-io/nkeys v0.4.7 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/opencontainers/selinux v1.10.0 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.59.1 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/stretchr/testify v1.10.0 // indirect
	go.opentelemetry.io/otel v1.30.0 // indirect
	go.opentelemetry.io/otel/trace v1.30.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/crypto v0.27.0 // indirect
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/oauth2 v0.23.0 // indirect
	golang.org/x/sync v0.8.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	golang.org/x/term v0.24.0 // indirect
	golang.org/x/text v0.18.0 // indirect
	golang.org/x/time v0.6.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240924160255-9d4c2d233b61 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	k8s.io/cloud-provider v0.25.0 // indirect
	k8s.io/component-base v0.31.0 // indirect
	k8s.io/csi-translation-lib v0.27.2 // indirect
	k8s.io/kube-openapi v0.0.0-20230501164219-8b0f38b5fd1f // indirect
	k8s.io/kube-scheduler v0.0.0 // indirect
	k8s.io/metrics v0.27.2 // indirect
	k8s.io/mount-utils v0.25.0 // indirect
	k8s.io/utils v0.0.0-20240711033017-18e509b52bc8 // indirect
	sigs.k8s.io/controller-runtime v0.15.2 // indirect
	sigs.k8s.io/json v0.0.0-20221116044647-bc3834ca7abd // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.4.1 // indirect
	sigs.k8s.io/yaml v1.4.0 // indirect
)

replace (
	git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc => ./hnc/
	github.com/go-kratos/kratos/v2 => github.com/go-kratos/kratos/v2 v2.7.2
	github.com/grpc-ecosystem/go-grpc-middleware/v2 => github.com/grpc-ecosystem/go-grpc-middleware/v2 v2.0.0-rc.3
	github.com/opencontainers/runc => github.com/opencontainers/runc v1.0.3
	go.opentelemetry.io/otel => go.opentelemetry.io/otel v1.10.0
	go.opentelemetry.io/otel/exporters/otlp/internal => go.opentelemetry.io/otel v1.10.0
	go.opentelemetry.io/otel/metric => go.opentelemetry.io/otel/metric v0.31.0
	go.opentelemetry.io/otel/sdk => go.opentelemetry.io/otel/sdk v1.10.0
	go.opentelemetry.io/otel/trace => go.opentelemetry.io/otel/trace v1.10.0
	k8s.io/api => k8s.io/api v0.27.2
	k8s.io/apiextensions-apiserver => k8s.io/apiextensions-apiserver v0.27.2
	k8s.io/apimachinery => k8s.io/apimachinery v0.27.2
	k8s.io/apiserver => k8s.io/apiserver v0.27.2
	k8s.io/cli-runtime => k8s.io/cli-runtime v0.27.2
	k8s.io/client-go => k8s.io/client-go v0.27.2
	k8s.io/cloud-provider => k8s.io/cloud-provider v0.27.2
	k8s.io/cluster-bootstrap => k8s.io/cluster-bootstrap v0.27.2
	k8s.io/code-generator => k8s.io/code-generator v0.27.2
	k8s.io/component-base => k8s.io/component-base v0.27.2
	k8s.io/component-helpers => k8s.io/component-helpers v0.27.2
	k8s.io/controller-manager => k8s.io/controller-manager v0.27.2
	k8s.io/cri-api => k8s.io/cri-api v0.27.2
	k8s.io/csi-translation-lib => k8s.io/csi-translation-lib v0.27.2
	k8s.io/dynamic-resource-allocation => k8s.io/dynamic-resource-allocation v0.27.2
	k8s.io/kube-aggregator => k8s.io/kube-aggregator v0.27.2
	k8s.io/kube-controller-manager => k8s.io/kube-controller-manager v0.27.2
	k8s.io/kube-proxy => k8s.io/kube-proxy v0.27.2
	k8s.io/kube-scheduler => k8s.io/kube-scheduler v0.27.2
	k8s.io/kubectl => k8s.io/kubectl v0.27.2
	k8s.io/kubelet => k8s.io/kubelet v0.27.2
	k8s.io/kubernetes => k8s.io/kubernetes v1.27.2
	k8s.io/legacy-cloud-providers => k8s.io/legacy-cloud-providers v0.27.2
	k8s.io/metrics => k8s.io/metrics v0.27.2
	k8s.io/mount-utils => k8s.io/mount-utils v0.27.2
	k8s.io/node-api => k8s.io/node-api v0.27.2
	k8s.io/pod-security-admission => k8s.io/pod-security-admission v0.27.2
	k8s.io/sample-apiserver => k8s.io/sample-apiserver v0.27.2
	k8s.io/sample-cli-plugin => k8s.io/sample-cli-plugin v0.27.2
	k8s.io/sample-controller => k8s.io/sample-controller v0.27.2
	volcano.sh/volcano => git-plat.tecorigin.net/ai-platform/volcano v1.8.0-alpha.0.0.20250207104647-931a4e6aa78e
)
