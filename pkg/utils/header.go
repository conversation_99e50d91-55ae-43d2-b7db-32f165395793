package utils

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"

	kjwt "github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/transport"

	"git-plat.tecorigin.net/ai-platform/backend-lib/casdoor"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecutil"
	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
)

func UserFromHeader(ctx context.Context) (*casdoor.User, error) {
	c, err := ClaimsFromHeader(ctx)
	if err != nil {
		return nil, err
	}
	return &c.User, nil
}

func ClaimsFromHeader(ctx context.Context) (*casdoor.Claims, error) {
	tr, ok := transport.FromServerContext(ctx)
	if !ok {
		return nil, kjwt.ErrMissingJwtToken
	}
	var c casdoor.Claims
	jsonStr := tr.RequestHeader().Get(tecons.TransHeaderClaims)
	if err := json.Unmarshal([]byte(jsonStr), &c); err != nil {
		return nil, tecutil.InternalErr(err)
	}
	return &c, nil
}

func ClaimsFromHeaderStr(ctx context.Context) (string, error) {
	tr, ok := transport.FromServerContext(ctx)
	if !ok {
		return "", kjwt.ErrMissingJwtToken
	}
	jsonStr := tr.RequestHeader().Get(tecons.TransHeaderClaims)
	return jsonStr, nil
}

func ClientIDFromHeader(ctx context.Context) (string, error) {
	tr, ok := transport.FromServerContext(ctx)
	if !ok {
		return "", errors.New("")
	}
	cID := tr.RequestHeader().Get(tecons.TransHeaderClientID)
	if cID == "" {
		return cID, errors.New("")
	}
	return cID, nil
}

func GetIDFromHeader(ctx context.Context) (
	projectId, workspaceId, tenantId int, err error) {

	tr, ok := transport.FromServerContext(ctx)
	if !ok {
		return 0, 0, 0, errV1.ErrorInvalidParams("GetIDFromHeader fail")
	}

	header := tr.RequestHeader()
	tID := header.Get(tecons.TransHeaderTenantID)
	tenantId, err = strconv.Atoi(tID)
	if err != nil {
		return 0, 0, 0, errV1.ErrorInvalidParams(
			"Convert tenant_id %s fail: %v", tID, err)
	}

	wID := header.Get(tecons.TransHeaderWorkspaceID)
	workspaceId, err = strconv.Atoi(wID)
	if err != nil {
		return 0, 0, 0, errV1.ErrorInvalidParams(
			"Convert workspace_id %s fail: %v", wID, err)
	}

	projectId = 0
	pID := header.Get(tecons.TransHeaderProjectID)
	if pID != "" {
		projectId, err = strconv.Atoi(pID)
		if err != nil {
			return 0, 0, 0, errV1.ErrorInvalidParams(
				"Convert project_id %s fail: %v", pID, err)
		}
	}

	return projectId, workspaceId, tenantId, nil
}
