package utils

import (
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
)

func MustDial(addr string) (*grpc.ClientConn, error) {
	c, err := grpc.Dial(addr, grpc.WithInsecure(),
		grpc.WithConnectParams(grpc.ConnectParams{
			MinConnectTimeout: time.Second * 3,
			Backoff:           backoff.DefaultConfig}))
	if err != nil {
		return nil, fmt.Errorf("dial grpc failed, err=%v, addr=%s", err, addr)
	}
	return c, nil
}
