package node

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"github.com/nats-io/nats.go/jetstream"
)

type IRepository interface {
	Label(
		ctx context.Context, node *models.Node, labels map[string]string) error
	UnLabel(ctx context.Context, node *models.Node, labels []string) error
	Get(ctx context.Context, node *models.Node) (*models.Node, error)

	List() ([]interface{}, error)
	GetNodesDetail(nodeItems []interface{}) ([]*models.Node, error)
	Disable(name string) error
	Enable(name string) error
}

type IMQRepository interface {
	// one consumber -> multi topic
	Subscribe(ctx context.Context, customer string, topic string,
		handler jetstream.MessageHandler) error
}
