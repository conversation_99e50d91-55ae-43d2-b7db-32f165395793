package node

import (
	"context"
	"sync"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/nats-io/nats.go/jetstream"
)

type natsRepository struct {
	natsClient *data.NatsJsClient

	consumerTopicMap map[string]map[string]jetstream.MessageHandler
	lock             sync.Mutex

	logger log.Logger
}

func NewNatsRepository(
	natsClient *data.NatsJsClient, logger log.Logger) IMQRepository {

	return &natsRepository{
		natsClient: natsClient,
		// consumber -> topic -> handler
		consumerTopicMap: map[string]map[string]jetstream.MessageHandler{},
		logger:           logger,
	}
}

func (repo *natsRepository) Subscribe(
	ctx context.Context, customer string, topic string,
	handler jetstream.MessageHandler) error {

	// insert to map.
	repo.lock.Lock()
	defer repo.lock.Unlock()
	if _, ok := repo.consumerTopicMap[customer]; !ok {
		repo.consumerTopicMap[customer] = map[string]jetstream.MessageHandler{}
	}
	repo.consumerTopicMap[customer][topic] = handler

	// subscribe to nats.
	topics := make([]string, 0)
	for topic := range repo.consumerTopicMap[customer] {
		topics = append(topics, topic)
	}
	// non-blocking, compete in the same consumer, share in different consumber.
	_, err := repo.natsClient.Subscribe(ctx, customer, repo.getHandler(customer), topics...)
	if err != nil {
		return errV1.ErrorNetworkError(
			"subscribe topic %v failed: %v", topics, err)
	}
	return nil
}

func (repo *natsRepository) getHandler(consumer string) jetstream.MessageHandler {
	return func(msg jetstream.Msg) {
		repo.consumerTopicMap[consumer][msg.Subject()](msg)
	}
}
