package node

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type k8sNodeRepo struct {
	k8sClient *data.K8sClient

	log *log.Helper
}

func NewK8sNodeRepo(k8sClient *data.K8sClient, logger log.Logger) IRepository {
	return &k8sNodeRepo{
		k8sClient: k8sClient,
		log:       log.NewHelper(logger),
	}
}

func (repo *k8sNodeRepo) Label(
	ctx context.Context, node *models.Node, labels map[string]string) error {

	// 创建合并补丁的 JSON 数据
	patchData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"labels": labels,
		},
	}
	patchBytes, err := json.Marshal(patchData)
	if err != nil {
		return errV1.ErrorUnknown("序列化补丁数据失败: %v", err)
	}

	// 使用 PATCH 方法更新节点
	_, err = repo.k8sClient.ClientSet.CoreV1().Nodes().Patch(
		ctx, node.Name, types.StrategicMergePatchType, patchBytes,
		metav1.PatchOptions{})
	if err != nil {
		return errV1.ErrorKubernetesOperatorError(
			"无法给节点（%s）打标签（%v）: %v", node.Name, labels, err)
	}

	return nil
}

func (repo *k8sNodeRepo) UnLabel(
	ctx context.Context, node *models.Node, labels []string) error {

	keys := make([]string, 0)
	for _, label := range labels {
		t := fmt.Sprintf(`"%s": null`, label)
		keys = append(keys, t)
	}

	keyStr := strings.Join(keys, ",")
	patchBytes := []byte(
		fmt.Sprintf(`{"metadata":{"labels":{%s}}}`, keyStr))

	// 执行 patch 操作
	_, err := repo.k8sClient.ClientSet.CoreV1().Nodes().Patch(
		ctx, node.Name, types.StrategicMergePatchType, patchBytes,
		metav1.PatchOptions{})
	if err != nil {
		return errV1.ErrorKubernetesOperatorError(
			"无法为节点（%s）删除标签（%v）: %v", node.Name, labels, err)
	}

	return nil
}

func (repo *k8sNodeRepo) Get(
	ctx context.Context, node *models.Node) (*models.Node, error) {

	// get
	k8sNode, err := repo.k8sClient.ClientSet.CoreV1().Nodes().Get(
		ctx, node.Name, metav1.GetOptions{})
	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}
	if err != nil {
		return nil, errV1.ErrorKubernetesOperatorError(
			"Get node %s failed: %v", node.Name, err)
	}

	// convert
	res := models.NewNodeFromK8sNode(k8sNode)
	return res, nil
}

func (repo *k8sNodeRepo) List() ([]interface{}, error) {
	return nil, errors.New("not implemented")
}

func (repo *k8sNodeRepo) GetNodesDetail(nodeItems []interface{}) ([]*models.Node, error) {
	return nil, errors.New("not implemented")
}

func (repo *k8sNodeRepo) Enable(name string) error {
	return errors.New("not implemented")
}

func (repo *k8sNodeRepo) Disable(name string) error {
	return errors.New("not implemented")
}
