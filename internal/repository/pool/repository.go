package pool

import (
	"context"

	tenantPb "git-plat.tecorigin.net/ai-platform/console-backend/api/tenant/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type RPCRepository struct {
	rpcCli *data.RpcClient
}

func NewRPCRepository(rpcCli *data.RpcClient) IRrpcRepository {
	return &RPCRepository{rpcCli: rpcCli}
}

func (r *RPCRepository) ListNodesBySpec(ctx context.Context, specId string) (
	[]*models.Node, error) {

	resp, err := r.rpcCli.TiCli.ListSupportNodes(
		ctx,
		&tenantPb.ListSupportPoolsRequest{
			SpecId: specId,
		})
	if err != nil {
		return nil, err
	}

	nodes := make([]*models.Node, 0)
	for _, one := range resp.Nodes {
		node := models.NewNodeFromListSupportNodesReply_One(one)
		nodes = append(nodes, node)
	}

	return nodes, nil
}
