package cache

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"github.com/nats-io/nats.go/jetstream"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/tools/cache"
	"volcano.sh/volcano/pkg/scheduler/api"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"
)

type IWatchRepository interface {
	Run(stopCh <-chan struct{}) error

	RegisterNodeHandler(cache.ResourceEventHandler)
	RegisterHrqHandler(cache.ResourceEventHandler)
	RegisterQuotaHandler(cache.ResourceEventHandler)
	RegisterTaskHandler(cache.ResourceEventHandler)

	GetNodeByUID(uid types.UID) *api.NodeInfo
	GetNodeByUIDs(uids sets.Set[types.UID]) map[types.UID]*api.NodeInfo
	GetNodeByName(name string) *api.NodeInfo
	ListNodesByLabels(labels map[string]string) []*api.NodeInfo

	GetNamespaceHrq(ctx context.Context, ns string) *models.NamespaceCollection
	GetNamespaceQuota(ctx context.Context, ns string) *schedulingapi.NamespaceCollection
}

type IMQRepository interface {
	// one consumber -> multi topic
	// subscribe from the now, neglect the history messages.
	Subscribe(ctx context.Context, customer string, topic string,
		handler jetstream.MessageHandler) error
	Public(ctx context.Context, subject string, msg interface{}) error
}
