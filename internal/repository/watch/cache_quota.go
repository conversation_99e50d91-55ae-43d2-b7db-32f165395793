package cache

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"
)

func (sc *SchedulerCache) GetNamespaceHrq(
	ctx context.Context, ns string) *models.NamespaceCollection {
	return sc.NamespaceHrqs[ns]
}

func (sc *SchedulerCache) GetNamespaceQuota(
	ctx context.Context, ns string) *schedulingapi.NamespaceCollection {
	return sc.NamespaceCollection[ns]
}
