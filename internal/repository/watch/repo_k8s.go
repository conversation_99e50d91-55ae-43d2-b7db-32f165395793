package cache

import (
	"context"
	"fmt"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"github.com/go-kratos/kratos/v2/log"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/tools/cache"
	"volcano.sh/volcano/pkg/scheduler/api"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"
)

type k8sWatchRepo struct {
	k8sClient *data.K8sClient

	cache Cache
	log   *log.Helper
}

func NewK8sWatchRepo(k8sClient *data.K8sClient, logger log.Logger) IWatchRepository {

	cache := New(k8sClient.Config, []string{"volcano"}, "default", nil, 1, nil)

	return &k8sWatchRepo{
		k8sClient: k8sClient,
		cache:     cache,
		log:       log.NewHelper(logger),
	}
}

func (repo *k8sWatchRepo) RegisterNodeHandler(handler cache.ResourceEventHandler) {
	repo.cache.RegisterNodeHandler(handler)
}

func (repo *k8sWatchRepo) RegisterHrqHandler(handler cache.ResourceEventHandler) {
	repo.cache.RegisterHrqHandler(handler)
}

func (repo *k8sWatchRepo) RegisterQuotaHandler(handler cache.ResourceEventHandler) {
	repo.cache.RegisterQuotaHandler(handler)
}

func (repo *k8sWatchRepo) RegisterTaskHandler(handler cache.ResourceEventHandler) {
	repo.cache.RegisterTaskHandler(handler)
}

func (repo *k8sWatchRepo) Run(stopCh <-chan struct{}) error {

	// Start cache for policy.
	repo.cache.SetMetricsConf(map[string]string{})
	repo.cache.Run(stopCh)
	repo.cache.WaitForCacheSync(stopCh)
	snapshot := repo.cache.Snapshot()
	fmt.Println(snapshot.String())
	return nil
}

func (repo *k8sWatchRepo) GetNodeByUID(uid types.UID) *api.NodeInfo {
	return repo.cache.GetNodeByUID(uid)
}

func (repo *k8sWatchRepo) GetNodeByUIDs(uids sets.Set[types.UID]) map[types.UID]*api.NodeInfo {
	return repo.cache.GetNodeByUIDs(uids)
}

func (repo *k8sWatchRepo) GetNodeByName(name string) *api.NodeInfo {
	return repo.cache.GetNodeByName(name)
}

func (repo *k8sWatchRepo) ListNodesByLabels(
	labels map[string]string) []*api.NodeInfo {
	return repo.cache.ListNodesByLabels(labels)
}

func (repo *k8sWatchRepo) GetNamespaceHrq(
	ctx context.Context, ns string) *models.NamespaceCollection {
	return repo.cache.GetNamespaceHrq(ctx, ns)
}

func (repo *k8sWatchRepo) GetNamespaceQuota(
	ctx context.Context, ns string) *schedulingapi.NamespaceCollection {
	return repo.cache.GetNamespaceQuota(ctx, ns)
}
