package cache

import (
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	"volcano.sh/volcano/pkg/scheduler/api"
)

func (sc *SchedulerCache) GetNodeByUID(uid types.UID) *api.NodeInfo {
	for _, node := range sc.Nodes {
		if node.Node == nil {
			continue
		}

		if node.Node.UID == uid {
			return node
		}
	}
	return nil
}

func (sc *SchedulerCache) GetNodeByUIDs(uids sets.Set[types.UID]) map[types.UID]*api.NodeInfo {
	nodes := make(map[types.UID]*api.NodeInfo)
	for _, node := range sc.Nodes {
		if node.Node == nil {
			continue
		}
		if uids.Has(node.Node.UID) {
			nodes[node.Node.UID] = node
		}
	}
	return nodes
}

func (sc *SchedulerCache) GetNodeByName(name string) *api.NodeInfo {
	ni := sc.Nodes[name]
	if ni == nil {
		return nil
	}

	sc.Mutex.Lock()
	defer sc.Mutex.Unlock()
	newNodeInfo := ni.Clone()
	return newNodeInfo
}

func MatchLabels(nodeLabels, selectorLabels map[string]string) bool {
	if len(selectorLabels) == 0 {
		return true
	}
	if len(nodeLabels) == 0 {
		return false
	}
	for key, value := range selectorLabels {
		if nodeValue, ok := nodeLabels[key]; !ok || nodeValue != value {
			return false
		}
	}
	return true
}

func (sc *SchedulerCache) ListNodesByLabels(labels map[string]string) []*api.NodeInfo {
	var nodes []*api.NodeInfo
	for _, node := range sc.Nodes {
		if node.Node == nil {
			continue
		}
		if MatchLabels(node.Node.Labels, labels) {
			nodes = append(nodes, node)
		}
	}
	return nodes
}
