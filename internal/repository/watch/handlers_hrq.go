package cache

import (
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
)

func (sc *SchedulerCache) AddHrq(obj interface{}) {
	hrq, ok := obj.(*hnc.HierarchicalResourceQuota)
	if !ok {
		klog.Errorf("Cannot convert to *hnc.HierarchicalResourceQuota: %v", obj)
		return
	}

	sc.Mutex.Lock()
	defer sc.Mutex.Unlock()

	klog.V(3).Infof("Add HRQ <%s/%v> in cache, with spec: %v.",
		hrq.Namespace, hrq.Name, hrq.Spec.Hard)
	sc.addOrUpdateHrq(hrq)
	go sc.hrqHandler.OnAdd(hrq, false)
}

func (sc *SchedulerCache) UpdateHrq(oldObj, newObj interface{}) {
	newR, ok := newObj.(*hnc.HierarchicalResourceQuota)
	if !ok {
		klog.Errorf(
			"Cannot convert newObj to *hnc.HierarchicalResourceQuota: %v", newObj)
		return
	}

	sc.Mutex.Lock()
	defer sc.Mutex.Unlock()

	klog.V(3).Infof("Update HRQ <%s/%v> in cache, with spec: %v.",
		newR.Namespace, newR.Name, newR.Spec.Hard)
	sc.addOrUpdateHrq(newR)
	go sc.hrqHandler.OnUpdate(oldObj, newObj)
}

func (sc *SchedulerCache) DeleteHrq(obj interface{}) {
	var h *hnc.HierarchicalResourceQuota
	switch t := obj.(type) {
	case *hnc.HierarchicalResourceQuota:
		h = t
	case cache.DeletedFinalStateUnknown:
		var ok bool
		h, ok = t.Obj.(*hnc.HierarchicalResourceQuota)
		if !ok {
			klog.Errorf(
				"Cannot convert to *hnc.HierarchicalResourceQuota: %v", t.Obj)
			return
		}
	default:
		klog.Errorf("Cannot convert to *hnc.HierarchicalResourceQuota: %v", t)
		return
	}

	sc.Mutex.Lock()
	defer sc.Mutex.Unlock()

	klog.V(3).Infof("Delete HRQ <%s/%v> in cache", h.Namespace, h.Name)
	sc.deleteHrq(h)
	go sc.hrqHandler.OnDelete(h)
}

func (sc *SchedulerCache) addOrUpdateHrq(hrq *hnc.HierarchicalResourceQuota) {
	collection, ok := sc.NamespaceHrqs[hrq.Namespace]
	if !ok {
		collection = models.NewNamespaceCollection(hrq.Namespace)
		sc.NamespaceHrqs[hrq.Namespace] = collection
	}

	collection.Update(hrq)
}

func (sc *SchedulerCache) deleteHrq(quota *hnc.HierarchicalResourceQuota) {
	collection, ok := sc.NamespaceHrqs[quota.Namespace]
	if !ok {
		return
	}

	collection.Delete(quota)
}
