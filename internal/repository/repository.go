package repository

import (
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/node"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/pool"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/secret"
	cache "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/watch"

	"github.com/google/wire"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	data.NewK8sClient, data.NewNatsJs, data.NewRpcClient,
	queue.NewK8sQueueRepo,
	namespace.NewK8sNamespaceRepo, namespace.NewK8sSubNamespaceRepo,
	quota.NewK8sQuotaRepo, quota.NewK8sHrqRepo,
	node.NewK8sNodeRepo, cache.NewK8sWatchRepo,
	cache.NewNatsRepository, node.NewNatsRepository, pool.NewRPCRepository,
	secret.NewK8sSecretRepo,
)
