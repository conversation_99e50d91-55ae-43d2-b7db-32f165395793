package queue

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type k8sQueueRepo struct {
	k8sClient *data.K8sClient

	log *log.Helper
}

func NewK8sQueueRepo(k8sClient *data.K8sClient, logger log.Logger) IRepo {
	return &k8sQueueRepo{
		k8sClient: k8sClient,
		log:       log.NewHelper(logger),
	}
}

func (repo *k8sQueueRepo) Create(ctx context.Context, q *models.Queue) error {
	_, err := repo.k8sClient.VcClientSet.SchedulingV1beta1().Queues().Create(
		ctx, q.ToK8sQueue(), metav1.CreateOptions{})
	if err != nil {
		return errV1.ErrorKubernetesOperatorError(
			"create queue %s failed: %v", q.Name, err)
	}
	return nil
}

func (repo *k8sQueueRepo) Get(
	ctx context.Context, q *models.Queue) (*models.Queue, error) {

	vcQueue, err := repo.k8sClient.VcClientSet.SchedulingV1beta1().Queues().Get(
		ctx, q.Name, metav1.GetOptions{})
	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}
	if err != nil {
		return nil, errV1.ErrorKubernetesOperatorError(
			"get queue %s failed: %v", q.Name, err)
	}

	res := models.NewQueueFromK8sQueue(vcQueue)
	return res, nil
}
