package data

import (
	"fmt"

	tecmqV1 "git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	"git-plat.tecorigin.net/ai-platform/backend-lib/v2/tecmq"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/nats-io/nats.go"
)

type NatsClient struct {
	NatsConn *nats.EncodedConn
	Log      *log.Helper
}

func NewNats(c *conf.Data, logger log.Logger) (*NatsClient, error) {
	ncCon, err := tecmqV1.NewEncodedNatsClient(c.Nats.Url)
	if err != nil {
		err := fmt.Errorf("new nats client fail, err=%v", err)
		log.NewHelper(logger).Errorf(err.<PERSON><PERSON>r())
		return nil, err
	}

	return &NatsClient{
		NatsConn: ncCon,
		Log:      log.NewHelper(logger),
	}, nil
}

type NatsJsClient struct {
	*tecmq.NatJs
	Log *log.Helper
}

func NewNatsJs(conf *conf.Data, logger log.Logger) (*NatsJsClient, func(), error) {
	if conf.Nats.Url == "" {
		err := fmt.Errorf("nats url is empty")
		log.NewHelper(logger).Errorf(err.Error())
		return nil, nil, err
	}

	nc, err := tecmq.NewNatsJsClient(conf.Nats.Url, tecons.NatsJetStreamAll)
	if err != nil {
		err := fmt.Errorf("nats client connect fail, err=%v", err)
		log.NewHelper(logger).Errorf(err.Error())
		return nil, nil, err
	}

	res := &NatsJsClient{
		NatJs: nc,
		Log:   log.NewHelper(logger),
	}
	cleanup := func() {
		res.Log.Info("disconnecting localNats~")
		nc.Close()
		res.Log.Info("localNats disconnected~")
	}
	return res, cleanup, nil
}
