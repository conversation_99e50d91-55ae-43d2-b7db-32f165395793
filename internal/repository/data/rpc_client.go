package data

import (
	tenantPb "git-plat.tecorigin.net/ai-platform/console-backend/api/tenant/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/pkg/utils"
)

type RpcClient struct {
	TiCli tenantPb.WorkbenchInternalClient
}

func NewRpcClient(tenant *conf.Tenant) (*RpcClient, error) {
	tenantConn, err := utils.MustDial(tenant.Server.Grpc.Addr)
	if err != nil {
		return nil, err
	}

	return &RpcClient{
		TiCli: tenantPb.NewWorkbenchInternalClient(tenantConn),
	}, nil
}
