package data

import (
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"path/filepath"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
	volcano "volcano.sh/apis/pkg/client/clientset/versioned"

	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/generated/clientset/versioned"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
)

type K8sClient struct {
	Config       *rest.Config
	ClientSet    *kubernetes.Clientset
	VcClientSet  *volcano.Clientset
	HncClientSet *hnc.Clientset

	Log *log.Helper
}

func NewK8sClient(
	k8sConfig *conf.K8SConfig, logger log.Logger) (*K8sClient, error) {

	var clientset *kubernetes.Clientset
	var config *rest.Config
	var err error

	if k8sConfig.DevEnv {
		// dev test local k8s cluster.
		tlsConfig := &tls.Config{InsecureSkipVerify: true}
		var transport http.RoundTripper = &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			TLSClientConfig:       tlsConfig,
			DisableCompression:    true,
		}
		config = &rest.Config{
			Host:        k8sConfig.DevConfig.Host,
			BearerToken: k8sConfig.DevConfig.BearerToken,
			Transport:   transport,
		}
	} else {
		config, err = getKubeConfig(k8sConfig, logger)
		if err != nil {
			err := fmt.Errorf("get k8s client config fail, err=%v", err)
			log.NewHelper(logger).Errorf(err.Error())
			return nil, err
		}
	}

	if k8sConfig.QPS > 0 {
		config.QPS = k8sConfig.QPS
	}
	if k8sConfig.Burst > 0 {
		config.Burst = int(k8sConfig.Burst)
	}

	clientset, err = kubernetes.NewForConfig(config)
	if err != nil {
		err := fmt.Errorf("get clientset fail, err=%v", err)
		log.NewHelper(logger).Errorf(err.Error())
		return nil, err
	}

	// get volcano client
	vcClientSet, err := volcano.NewForConfig(config)
	if err != nil {
		err := fmt.Errorf("get volcano clientset fail, err=%v", err)
		log.NewHelper(logger).Errorf(err.Error())
		return nil, err
	}

	// get hrq client
	hncClient, err := hnc.NewForConfig(config)
	if err != nil {
		err := fmt.Errorf("get hnc clientset fail, err=%v", err)
		log.NewHelper(logger).Errorf(err.Error())
		return nil, err
	}

	return &K8sClient{
		Config:       config,
		ClientSet:    clientset,
		VcClientSet:  vcClientSet,
		HncClientSet: hncClient,
		Log:          log.NewHelper(logger),
	}, nil
}

func getKubeConfig(k8sConfig *conf.K8SConfig, logger log.Logger) (
	*rest.Config, error) {

	config, err := rest.InClusterConfig()
	if err == nil {
		return config, nil
	}

	if err == rest.ErrNotInCluster && !k8sConfig.OutCluster {
		log.NewHelper(logger).Errorf("get in cluster conf fail, err=%w", err)
		return nil, err
	}

	if err == rest.ErrNotInCluster {
		kubeconfig := k8sConfig.Kubeconfig
		if kubeconfig == "" {
			kubeconfig = filepath.Join(homedir.HomeDir(), ".kube", "config")
		}
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			log.NewHelper(logger).Errorf(
				"get out cluster conf fail, err=%w", err)
			return nil, err
		}
		return config, nil
	}

	return nil, err
}
