package quota

import (
	"context"

	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
)

type IQuotaRepo interface {
	Create(context.Context, *corev1.ResourceQuota) (*corev1.ResourceQuota, error)
	Delete(context.Context, *corev1.ResourceQuota) error
	Update(context.Context, *corev1.ResourceQuota) (*corev1.ResourceQuota, error)
	Get(context.Context, *corev1.ResourceQuota) (*corev1.ResourceQuota, error)
}

// hierarchicalresourcequotas.
type IHRQRepo interface {
	Create(context.Context, *hnc.HierarchicalResourceQuota) (
		*hnc.HierarchicalResourceQuota, error)
	Delete(context.Context, *hnc.HierarchicalResourceQuota) error
	Update(context.Context, *hnc.HierarchicalResourceQuota) (
		*hnc.HierarchicalResourceQuota, error)
	Patch(ctx context.Context,
		od, new *hnc.HierarchicalResourceQuota) (
		*hnc.HierarchicalResourceQuota, error)
	Get(context.Context, *hnc.HierarchicalResourceQuota) (
		*hnc.HierarchicalResourceQuota, error)
	List(context.Context, string, labels.Selector) (
		[]hnc.HierarchicalResourceQuota, error)
}
