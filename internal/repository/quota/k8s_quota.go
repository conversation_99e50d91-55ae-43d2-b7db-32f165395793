package quota

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
)

type k8sQuotaRepo struct {
	k8sClient *data.K8sClient

	log *log.Helper
}

func NewK8sQuotaRepo(k8sClient *data.K8sClient, logger log.Logger) IQuotaRepo {
	return &k8sQuotaRepo{
		k8sClient: k8sClient,
		log:       log.NewHelper(logger),
	}
}

func (repo *k8sQuotaRepo) Create(
	ctx context.Context, quota *corev1.ResourceQuota) (*corev1.ResourceQuota, error) {

	res, err := repo.k8sClient.ClientSet.CoreV1().ResourceQuotas(
		quota.Namespace).Create(ctx, quota, metav1.CreateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"create quota %s(%s) failed: %v", quota.Name, quota.Namespace, err)
	}
	return res, nil
}

func (repo *k8sQuotaRepo) Delete(
	ctx context.Context, quota *corev1.ResourceQuota) error {

	err := repo.k8sClient.ClientSet.CoreV1().ResourceQuotas(
		quota.Namespace).Delete(ctx, quota.Name, metav1.DeleteOptions{})
	if err != nil && !k8sErr.IsNotFound(err) {
		return errV1.ErrorKubernetesOperatorError(
			"delete quota %s(%s) failed: %v", quota.Name, quota.Namespace, err)
	}
	return nil
}

func (repo *k8sQuotaRepo) Update(
	ctx context.Context, quota *corev1.ResourceQuota) (
	*corev1.ResourceQuota, error) {

	res, err := repo.k8sClient.ClientSet.CoreV1().ResourceQuotas(
		quota.Namespace).Update(ctx, quota, metav1.UpdateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"update quota %s(%s) failed: %v", quota.Name, quota.Namespace, err)
	}
	return res, nil
}

func (repo *k8sQuotaRepo) Get(
	ctx context.Context, quota *corev1.ResourceQuota) (
	*corev1.ResourceQuota, error) {

	res, err := repo.k8sClient.ClientSet.CoreV1().ResourceQuotas(
		quota.Namespace).Get(ctx, quota.Name, metav1.GetOptions{})

	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}

	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"get quota %s(%s) failed: %v",
			quota.Name, quota.Namespace, err)
	}

	return res, nil
}
