package quota

import (
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"gomodules.xyz/jsonpatch/v2"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
)

type k8sHrqRepo struct {
	k8sClient *data.K8sClient

	log *log.Helper
}

func NewK8sHrqRepo(k8sClient *data.K8sClient, logger log.Logger) IHRQRepo {
	return &k8sHrqRepo{
		k8sClient: k8sClient,
		log:       log.<PERSON><PERSON><PERSON>(logger),
	}
}

func (repo *k8sHrqRepo) Create(
	ctx context.Context, hrq *hnc.HierarchicalResourceQuota) (
	*hnc.HierarchicalResourceQuota, error) {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.HierarchicalResourceQuotas(hrq.Namespace).Create(
		ctx, hrq, metav1.CreateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"create Hrq %s(%s) failed: %v", hrq.Name, hrq.Namespace, err)
	}
	return res, nil
}

func (repo *k8sHrqRepo) Delete(
	ctx context.Context, hrq *hnc.HierarchicalResourceQuota) error {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	err := hncApi.HierarchicalResourceQuotas(hrq.Namespace).Delete(
		ctx, hrq.Name, metav1.DeleteOptions{})
	if err != nil && !k8sErr.IsNotFound(err) {
		return errV1.ErrorKubernetesOperatorError(
			"delete Hrq %s(%s) failed: %v", hrq.Name, hrq.Namespace, err)
	}
	return nil
}

func (repo *k8sHrqRepo) Update(
	ctx context.Context, hrq *hnc.HierarchicalResourceQuota) (
	*hnc.HierarchicalResourceQuota, error) {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.HierarchicalResourceQuotas(hrq.Namespace).Update(
		ctx, hrq, metav1.UpdateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"update Hrq %s(%s) failed: %v", hrq.Name, hrq.Namespace, err)
	}
	return res, nil
}

func (repo *k8sHrqRepo) Patch(
	ctx context.Context, old, new *hnc.HierarchicalResourceQuota) (
	*hnc.HierarchicalResourceQuota, error) {

	before, _ := json.Marshal(old)
	after, _ := json.Marshal(new)

	patches, err := jsonpatch.CreatePatch(before, after)
	if err != nil {
		return nil, errV1.ErrorUnknown("unable create json patch: %v", err)
	}

	data, err := json.Marshal(patches)
	if err != nil {
		return nil, errV1.ErrorUnknown("unable marshal json patch: %v", err)
	}

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.HierarchicalResourceQuotas(new.Namespace).Patch(
		ctx, new.Name, types.JSONPatchType, data, metav1.PatchOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"patch Hrq %s(%s) failed: %v", new.Name, new.Namespace, err)
	}
	return res, nil
}

func (repo *k8sHrqRepo) Get(
	ctx context.Context, hrq *hnc.HierarchicalResourceQuota) (
	*hnc.HierarchicalResourceQuota, error) {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.HierarchicalResourceQuotas(hrq.Namespace).Get(
		ctx, hrq.Name, metav1.GetOptions{})
	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"get Hrq %s(%s) failed: %v", hrq.Name, hrq.Namespace, err)
	}
	return res, nil
}

func (repo *k8sHrqRepo) List(
	ctx context.Context, namespace string, selector labels.Selector) (
	[]hnc.HierarchicalResourceQuota, error) {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.HierarchicalResourceQuotas(namespace).List(
		ctx, metav1.ListOptions{LabelSelector: selector.String()})
	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}
	if err != nil {
		return nil, errV1.ErrorKubernetesOperatorError(
			"list Hrq failed: %v", err)
	}
	return res.Items, nil
}
