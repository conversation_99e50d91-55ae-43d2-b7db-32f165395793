package namespace

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
)

type k8sNamespaceRepo struct {
	k8sClient *data.K8sClient

	log *log.Helper
}

func NewK8sNamespaceRepo(
	k8sClient *data.K8sClient, logger log.Logger) INamespaceRepo {

	return &k8sNamespaceRepo{
		k8sClient: k8sClient,
		log:       log.NewHelper(logger),
	}
}

func (repo *k8sNamespaceRepo) Create(
	ctx context.Context, ns *corev1.Namespace) (*corev1.Namespace, error) {

	res, err := repo.k8sClient.ClientSet.CoreV1().Namespaces().Create(
		ctx, ns, metav1.CreateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"Create namespace %s failed: %v", ns.Name, err)
	}
	return res, nil
}

func (repo *k8sNamespaceRepo) Delete(
	ctx context.Context, ns *corev1.Namespace) error {

	err := repo.k8sClient.ClientSet.CoreV1().Namespaces().Delete(
		ctx, ns.Name, metav1.DeleteOptions{})
	if err != nil && !k8sErr.IsNotFound(err) {
		return errV1.ErrorKubernetesOperatorError(
			"Delete namespace %s failed: %v", ns.Name, err)
	}
	return nil
}

func (repo *k8sNamespaceRepo) Update(
	ctx context.Context, ns *corev1.Namespace) (*corev1.Namespace, error) {

	modNs, err := repo.k8sClient.ClientSet.CoreV1().Namespaces().Update(
		ctx, ns, metav1.UpdateOptions{})
	if err != nil {
		return modNs, errV1.ErrorKubernetesOperatorError(
			"Update namespace %s failed: %v", ns.Name, err)
	}
	return modNs, nil
}

func (repo *k8sNamespaceRepo) Get(
	ctx context.Context, ns *corev1.Namespace) (*corev1.Namespace, error) {

	k8sNs, err := repo.k8sClient.ClientSet.CoreV1().Namespaces().Get(
		ctx, ns.Name, metav1.GetOptions{})

	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}

	if err != nil {
		return k8sNs, errV1.ErrorKubernetesOperatorError(
			"Get namespace %s failed: %v", ns.Name, err)
	}

	return k8sNs, nil
}
