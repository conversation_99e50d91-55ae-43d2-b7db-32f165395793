package namespace

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"
)

type k8sSubNamespaceRepo struct {
	k8sClient *data.K8sClient

	log *log.Helper
}

func NewK8sSubNamespaceRepo(
	k8sClient *data.K8sClient, logger log.Logger) ISubNamespaceRepo {

	return &k8sSubNamespaceRepo{
		k8sClient: k8sClient,
		log:       log.<PERSON><PERSON><PERSON><PERSON>(logger),
	}
}

func (repo *k8sSubNamespaceRepo) Create(
	ctx context.Context, subns *hnc.SubnamespaceAnchor) (
	*hnc.SubnamespaceAnchor, error) {

	res, err := repo.k8sClient.HncClientSet.HncV1alpha2().SubnamespaceAnchors(
		subns.Namespace).Create(ctx, subns, metav1.CreateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"create SubNamespace %s(%s) failed: %v",
			subns.Name, subns.Namespace, err)
	}
	return res, nil
}

func (repo *k8sSubNamespaceRepo) Delete(
	ctx context.Context, subns *hnc.SubnamespaceAnchor) error {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	err := hncApi.SubnamespaceAnchors(subns.Namespace).Delete(
		ctx, subns.Name, metav1.DeleteOptions{})
	if err != nil && !k8sErr.IsNotFound(err) {
		return errV1.ErrorKubernetesOperatorError(
			"delete SubNamespace %s(%s) failed: %v",
			subns.Name, subns.Namespace, err)
	}
	return nil
}

func (repo *k8sSubNamespaceRepo) Update(
	ctx context.Context, subns *hnc.SubnamespaceAnchor) (
	*hnc.SubnamespaceAnchor, error) {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.SubnamespaceAnchors(subns.Namespace).Update(
		ctx, subns, metav1.UpdateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"update SubNamespace %s(%s) failed: %v",
			subns.Name, subns.Namespace, err)
	}
	return res, nil
}

func (repo *k8sSubNamespaceRepo) Get(
	ctx context.Context, subns *hnc.SubnamespaceAnchor) (
	*hnc.SubnamespaceAnchor, error) {

	hncApi := repo.k8sClient.HncClientSet.HncV1alpha2()
	res, err := hncApi.SubnamespaceAnchors(subns.Namespace).Get(
		ctx, subns.Name, metav1.GetOptions{})
	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"get SubNamespace %s(%s) failed: %v",
			subns.Name, subns.Namespace, err)
	}

	return res, nil
}
