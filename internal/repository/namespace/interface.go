package namespace

import (
	"context"

	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"

	corev1 "k8s.io/api/core/v1"
)

type INamespaceRepo interface {
	Create(context.Context, *corev1.Namespace) (*corev1.Namespace, error)
	Delete(context.Context, *corev1.Namespace) error
	Update(context.Context, *corev1.Namespace) (*corev1.Namespace, error)
	Get(context.Context, *corev1.Namespace) (*corev1.Namespace, error)
}

type ISubNamespaceRepo interface {
	Create(context.Context, *hnc.SubnamespaceAnchor) (
		*hnc.SubnamespaceAnchor, error)
	Delete(context.Context, *hnc.SubnamespaceAnchor) error
	Update(context.Context, *hnc.SubnamespaceAnchor) (
		*hnc.SubnamespaceAnchor, error)
	Get(context.Context, *hnc.SubnamespaceAnchor) (
		*hnc.SubnamespaceAnchor, error)
}
