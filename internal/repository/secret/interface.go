package secret

import (
	"context"

	corev1 "k8s.io/api/core/v1"
)

// ISecretRepo 定义了K8S Secret的仓库接口
type ISecretRepo interface {
	// Create 创建一个新的Secret
	Create(context.Context, *corev1.Secret) (*corev1.Secret, error)

	// Delete 删除指定的Secret
	Delete(context.Context, *corev1.Secret) error

	// Update 更新指定的Secret
	Update(context.Context, *corev1.Secret) (*corev1.Secret, error)

	// Get 获取指定的Secret
	Get(context.Context, *corev1.Secret) (*corev1.Secret, error)

	// Apply 应用指定的Secret
	Apply(context.Context, *corev1.Secret) (*corev1.Secret, error)
}
