package secret

import (
	"context"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/data"

	v1 "k8s.io/api/core/v1"
	k8sErr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	applyCoreV1 "k8s.io/client-go/applyconfigurations/core/v1"
	applyMetaV1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// K8sSecretRepo 实现了ISecretRepo接口
type K8sSecretRepo struct {
	k8sClient *data.K8sClient
}

// NewK8sSecretRepo 创建一个新的SecretRepo实例
func NewK8sSecretRepo(client *data.K8sClient) ISecretRepo {
	return &K8sSecretRepo{
		k8sClient: client,
	}
}

// Create 创建一个新的Secret
func (r *K8sSecretRepo) Create(ctx context.Context, secret *v1.Secret) (
	*v1.Secret, error) {
	res, err := r.k8sClient.ClientSet.CoreV1().Secrets(secret.Namespace).Create(
		ctx, secret, metav1.CreateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"Create secret %s(%s) failed: %v", secret.Name, secret.Namespace, err)
	}
	return res, nil
}

// Delete 删除指定的Secret
func (r *K8sSecretRepo) Delete(ctx context.Context, secret *v1.Secret) error {
	err := r.k8sClient.ClientSet.CoreV1().Secrets(secret.Namespace).Delete(
		ctx, secret.Name, metav1.DeleteOptions{})
	if err != nil {
		return errV1.ErrorKubernetesOperatorError(
			"Delete secret %s(%s) failed: %v", secret.Name, secret.Namespace, err)
	}
	return nil
}

// Update 更新指定的Secret
func (r *K8sSecretRepo) Update(ctx context.Context, secret *v1.Secret) (
	*v1.Secret, error) {
	res, err := r.k8sClient.ClientSet.CoreV1().Secrets(secret.Namespace).Update(
		ctx, secret, metav1.UpdateOptions{})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"Update secret %s(%s) failed: %v", secret.Name, secret.Namespace, err)
	}
	return res, nil
}

// Get 获取指定的Secret
func (r *K8sSecretRepo) Get(ctx context.Context, secret *v1.Secret) (
	*v1.Secret, error) {
	secret, err := r.k8sClient.ClientSet.CoreV1().Secrets(secret.Namespace).Get(
		ctx, secret.Name, metav1.GetOptions{})
	if err != nil && k8sErr.IsNotFound(err) {
		return nil, nil
	}
	if err != nil {
		return secret, errV1.ErrorKubernetesOperatorError(
			"Get secret %s(%s) failed: %v", secret.Name, secret.Namespace, err)
	}
	return secret, nil
}

// Apply 应用指定的Secret
func (r *K8sSecretRepo) Apply(ctx context.Context, secret *v1.Secret) (
	*v1.Secret, error) {

	// 创建 ApplyConfiguration
	apiVersion := "v1"
	kind := "Secret"
	applyConfig := applyCoreV1.SecretApplyConfiguration{
		TypeMetaApplyConfiguration: applyMetaV1.TypeMetaApplyConfiguration{
			Kind:       &kind,
			APIVersion: &apiVersion,
		},
		ObjectMetaApplyConfiguration: &applyMetaV1.ObjectMetaApplyConfiguration{
			Name:        &secret.Name,
			Namespace:   &secret.Namespace,
			Labels:      secret.Labels,
			Annotations: secret.Annotations,
		},
		Data:       secret.Data,
		StringData: secret.StringData,
		Type:       &secret.Type,
	}

	// 调用Apply方法，添加Force选项
	res, err := r.k8sClient.ClientSet.CoreV1().Secrets(secret.Namespace).Apply(
		ctx,
		&applyConfig,
		metav1.ApplyOptions{
			FieldManager: "scheduling-manager",
			Force:        true, // 添加Force选项来解决冲突
		})
	if err != nil {
		return res, errV1.ErrorKubernetesOperatorError(
			"Apply secret %s(%s) failed: %v", secret.Name, secret.Namespace, err)
	}
	return res, nil
}
