package service

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/types/known/emptypb"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	v1 "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/node"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/pool"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/tenant"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/workspace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/pkg/utils"
)

// SchedulingApi is a greeter service.
type SchedulingApi struct {
	v1.UnimplementedSchedulingApiServer

	tenantUsecase tenant.IUsecase
	wsUsecase     workspace.IUsecase
	nodeUsecase   node.IUsecase
	queueUsecase  queue.IUsecase
	watchUsecase  watch.IUsecase
	poolUsecase   pool.IUsecase
}

// NewSchedulingApi new a greeter service.
func NewSchedulingApi(
	tenantUsecase tenant.IUsecase, wsUsecase workspace.IUsecase,
	nodeUsecase node.IUsecase, queueUsecase queue.IUsecase,
	watchUsecase watch.IUsecase, poolUsecase pool.IUsecase) *SchedulingApi {

	err := watchUsecase.Init()
	if err != nil {
		panic(err)
	}

	err = queueUsecase.Init()
	if err != nil {
		panic(err)
	}

	err = poolUsecase.Init()
	if err != nil {
		panic(err)
	}

	err = nodeUsecase.Init()
	if err != nil {
		panic(err)
	}

	return &SchedulingApi{
		tenantUsecase: tenantUsecase,
		wsUsecase:     wsUsecase,
		nodeUsecase:   nodeUsecase,
		queueUsecase:  queueUsecase,
		watchUsecase:  watchUsecase,
		poolUsecase:   poolUsecase,
	}
}

func (s *SchedulingApi) CreatePod(
	ctx context.Context, req *v1.CreatePodRequest) (*v1.CreatePodReply, error) {
	return &v1.CreatePodReply{}, nil
}
func (s *SchedulingApi) CreatePods(
	ctx context.Context, req *v1.CreatePodsRequest) (*v1.CreatePodsReply, error) {
	return &v1.CreatePodsReply{}, nil
}
func (s *SchedulingApi) QueryPodCount(
	ctx context.Context, req *v1.CreatePodRequest) (
	*v1.QueryPodCountReply, error) {
	return &v1.QueryPodCountReply{}, nil
}

func (s *SchedulingApi) CreateTenant(
	ctx context.Context, req *v1.CreateTenantRequest) (
	*v1.CreateTenantReply, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	tenant := models.NewTenantFromPBCreateRequest(req)
	k8sNs, err := s.tenantUsecase.CreateTenant(ctx, tenant)
	if err != nil {
		return nil, err
	}

	// form resturn
	res := make(map[int32]string)
	for pool, ns := range k8sNs {
		res[int32(pool)] = ns
	}
	return &v1.CreateTenantReply{
		Namespaces: res,
	}, nil
}

func (s *SchedulingApi) RemoveTenant(
	ctx context.Context, req *v1.RemoveTenantRequest) (*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	tenant := models.NewTenantFromPBRemoveRequest(req)

	err := s.tenantUsecase.RemoveTenant(ctx, tenant)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) UpdateTenant(
	ctx context.Context, req *v1.CreateTenantRequest) (*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	tenant := models.NewTenantFromPBCreateRequest(req)
	err := s.tenantUsecase.UpdateTenant(ctx, tenant)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) SyncTenant(
	ctx context.Context, req *v1.CreateTenantRequest) (
	*v1.CreateTenantReply, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	tenant := models.NewTenantFromPBCreateRequest(req)
	k8sNs, err := s.tenantUsecase.SyncTenant(ctx, tenant)
	if err != nil {
		return nil, err
	}

	// form return
	res := make(map[int32]string)
	for pool, ns := range k8sNs {
		res[int32(pool)] = ns
	}
	return &v1.CreateTenantReply{
		Namespaces: res,
	}, nil
}

func (s *SchedulingApi) CreateWorkspace(
	ctx context.Context, req *v1.CreateWorkspaceRequest) (
	*v1.CreateWorkspaceReply, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	workspace := models.NewWorkspaceFromPBCreateRequest(req)
	k8sNs, err := s.wsUsecase.CreateWorkspace(ctx, workspace)
	if err != nil {
		return nil, err
	}

	// form resturn
	res := make(map[int32]string)
	for pool, ns := range k8sNs {
		res[int32(pool)] = ns
	}
	return &v1.CreateWorkspaceReply{
		Namespaces: res,
	}, nil
}

func (s *SchedulingApi) RemoveWorkspace(
	ctx context.Context, req *v1.RemoveWorkspaceRequest) (*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	workspace := models.NewWorkspaceFromPBRemoveRequest(req)
	err := s.wsUsecase.RemoveWorkspace(ctx, workspace)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) UpdateWorkspace(
	ctx context.Context, req *v1.CreateWorkspaceRequest) (*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	workspace := models.NewWorkspaceFromPBCreateRequest(req)
	err := s.wsUsecase.UpdateWorkspace(ctx, workspace)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) SyncWorkspace(
	ctx context.Context, req *v1.CreateWorkspaceRequest) (
	*v1.CreateWorkspaceReply, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	workspace := models.NewWorkspaceFromPBCreateRequest(req)
	k8sNs, err := s.wsUsecase.SyncWorkspace(ctx, workspace)
	if err != nil {
		return nil, err
	}

	// form resturn
	res := make(map[int32]string)
	for pool, ns := range k8sNs {
		res[int32(pool)] = ns
	}
	return &v1.CreateWorkspaceReply{
		Namespaces: res,
	}, nil
}

func (s *SchedulingApi) LableNode(
	ctx context.Context, req *v1.LabelNodeRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, nil
}
func (s *SchedulingApi) UnlabelNode(
	ctx context.Context, req *v1.UnlabelNodeRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) AllocateNodeToTenant(
	ctx context.Context, req *v1.AllocateNodeToTenantRequest) (
	*emptypb.Empty, error) {
	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	node := &models.Node{Name: req.NodeName}
	tenant := &models.Tenant{Id: req.TenantId}
	err := s.nodeUsecase.AllocateNodeToTenant(ctx, node, tenant)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) DeallocateNodeToTenant(
	ctx context.Context, req *v1.AllocateNodeToTenantRequest) (
	*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	node := &models.Node{Name: req.NodeName}
	tenant := &models.Tenant{Id: req.TenantId}
	err := s.nodeUsecase.DeallocateNodeToTenant(ctx, node, tenant)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) AllocateNodeToWorkspace(
	ctx context.Context, req *v1.AllocateNodeToWorkspaceRequest) (
	*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	node := &models.Node{
		Name: req.NodeName,
	}
	workspace := &models.Workspace{Id: req.WorkspaceId}
	err := s.nodeUsecase.AllocateNodeToWorkspace(ctx, node, workspace)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) DeallocateNodeToWorkspace(
	ctx context.Context, req *v1.AllocateNodeToWorkspaceRequest) (
	*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	node := &models.Node{
		Name: req.NodeName,
	}
	workspace := &models.Workspace{Id: req.WorkspaceId}
	err := s.nodeUsecase.DeallocateNodeToWorkspace(ctx, node, workspace)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) AllocateNodeToResourcePool(
	ctx context.Context, req *v1.AllocateNodeToResourcePoolRequest) (
	*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	node := &models.Node{
		Name: req.NodeName,
	}
	pool := models.ResourcePoolType(req.Pool)
	err := s.nodeUsecase.AllocateNodeToResourcePool(ctx, node, pool, req.PoolId)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) DeallocateNodeToResourcePool(
	ctx context.Context, req *v1.AllocateNodeToResourcePoolRequest) (
	*emptypb.Empty, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model & resolve
	node := &models.Node{
		Name: req.NodeName,
	}
	pool := models.ResourcePoolType(req.Pool)
	err := s.nodeUsecase.DeallocateNodeToResourcePool(ctx, node, pool, req.PoolId)
	if err != nil {
		return nil, err
	}

	// form resturn
	return &emptypb.Empty{}, nil
}

func (s *SchedulingApi) ResourceAvailable(
	ctx context.Context, req *v1.ResourceAvailableRequest) (
	*v1.ResourceAvailableReply, error) {

	// validate
	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}
	if len(req.Configurations) == 0 {
		return nil, errV1.ErrorInvalidParams("configurations is empty")
	}

	// form model & resolve
	_, workspaceId, tenantId, err := utils.GetIDFromHeader(ctx)
	if err != nil {
		return nil, err
	}
	workspace := &models.Workspace{
		Id:       strconv.Itoa(workspaceId),
		TenantId: strconv.Itoa(tenantId),
	}
	pool := models.ResourcePoolType(req.Pool)
	usage, err := models.NewQuotaTypedUsageFromSpecConfiguration(req.Configurations)
	if err != nil {
		return nil, err
	}
	available, reason, keyResources, detail, err := s.poolUsecase.ResourceAvailable(
		ctx, pool, workspace, req.SpecId, usage)
	if err != nil {
		return nil, err
	}

	return &v1.ResourceAvailableReply{
		Available:    available,
		Reason:       reason.String(),
		KeyResources: keyResources,
		Detail:       detail,
	}, nil
}
