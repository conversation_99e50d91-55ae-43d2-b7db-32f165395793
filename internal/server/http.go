package server

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/backend-lib/metrics"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	v1 "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/service"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/version"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/justinas/alice"
	"github.com/prometheus/client_golang/prometheus"
	metricsprom "github.com/slok/go-http-metrics/metrics/prometheus"
	metricsmdlw "github.com/slok/go-http-metrics/middleware"
	metricsstd "github.com/slok/go-http-metrics/middleware/std"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/encoding/protojson"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(
	c *conf.Server, api *service.SchedulingApi, logger log.Logger) *http.Server {

	metrics.InitMetrics(
		version.Name, version.Version, version.GitCommit, version.BuildTS)
	gwmux := runtime.NewServeMux(
		runtime.WithMarshalerOption(
			runtime.MIMEWildcard,
			&runtime.JSONPb{
				MarshalOptions: protojson.MarshalOptions{
					UseEnumNumbers:  true,
					EmitUnpopulated: true,
				},
				UnmarshalOptions: protojson.UnmarshalOptions{
					DiscardUnknown: true,
				},
			}),
		runtime.WithIncomingHeaderMatcher(CustomMatcher),
		runtime.WithErrorHandler(metrics.ErrorHandler),
	)

	err := v1.RegisterSchedulingApiHandlerFromEndpoint(
		context.TODO(), gwmux, c.Grpc.Addr,
		[]grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		},
	)
	if err != nil {
		log.Fatal("failed to register grpc gateway", zap.Error(err))
	}

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)

	// http metrics
	mdlw := metricsmdlw.New(
		metricsmdlw.Config{
			Recorder: metricsprom.NewRecorder(
				metricsprom.Config{Registry: prometheus.DefaultRegisterer}),
		})
	h := alice.New(metricsstd.HandlerProvider("", mdlw)).Then(gwmux)
	srv.HandlePrefix("/", h)

	// v1.RegisterSchedulingApiHTTPServer(srv, api)
	return srv
}

func CustomMatcher(key string) (string, bool) {
	switch key {
	case tecons.TransHeaderClientID, tecons.TransHeaderClaims, tecons.TransHeaderTenantID:
		return key, true
	case tecons.TransHeaderWorkspaceID:
		return key, true
	case tecons.TransHeaderProjectID:
		return key, true
	case tecons.TransHeaderCasbinSub:
		return key, true
	case tecons.TransHeaderClusterID:
		return key, true
	default:
		return runtime.DefaultHeaderMatcher(key)
	}
}
