package server

import (
	v1 "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	grpcmetrics "github.com/grpc-ecosystem/go-grpc-middleware/providers/openmetrics/v2"
	grpcrecovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	"github.com/prometheus/client_golang/prometheus"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(
	c *conf.Server, api *service.SchedulingApi, logger log.Logger) *grpc.Server {

	sm := grpcmetrics.NewServerMetrics(
		grpcmetrics.WithServerHandlingTimeHistogram())

	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
		),
		grpc.StreamInterceptor(
			grpcrecovery.StreamServerInterceptor(),
			grpcmetrics.StreamServerInterceptor(sm),
		),
		grpc.UnaryInterceptor(
			grpcrecovery.UnaryServerInterceptor(),
			grpcmetrics.UnaryServerInterceptor(sm),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)

	v1.RegisterSchedulingApiServer(srv, api)
	sm.MustRegister(prometheus.DefaultRegisterer)
	sm.InitializeMetrics(srv.Server)
	return srv
}
