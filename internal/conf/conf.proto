syntax = "proto3";
package kratos.api;

option go_package = "scheduling-manager/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  K8sConfig k8sConfig = 3;
  Tenant tenant = 4;
  Harbor harbor = 5;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration read_timeout = 3;
    google.protobuf.Duration write_timeout = 4;
  }
  message Nats {
    string url = 1;
  }
  Database database = 1;
  Redis redis = 2;
  Nats nats = 3;
}

message DevConfig {
  string host = 1;
  string bearerToken = 2;
}

message K8sConfig {
  bool outCluster = 1;
  string kubeconfig = 2;
  DevConfig devConfig = 3;
  bool devEnv = 4;
  float QPS = 5;
  int32 Burst = 6;
}

message Tenant{
  Server server = 1;
}

message Harbor {
  string url = 1;
  string username = 2;
  string password = 3;
  bool insecureRegistry = 4;
  string secretName = 5;
}
