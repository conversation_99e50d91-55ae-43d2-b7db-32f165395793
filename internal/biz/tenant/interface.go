package tenant

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type IUsecase interface {
	CreateTenant(context.Context, *models.Tenant) (
		map[models.ResourcePoolType]string, error)
	RemoveTenant(context.Context, *models.Tenant) error
	UpdateTenant(context.Context, *models.Tenant) error
	SyncTenant(context.Context, *models.Tenant) (
		map[models.ResourcePoolType]string, error)
}
