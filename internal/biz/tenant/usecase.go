package tenant

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/secret"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type tenantUsecase struct {
	nsRepo     namespace.INamespaceRepo
	subnsRepo  namespace.ISubNamespaceRepo
	hrqRepo    quota.IHRQRepo
	secretRepo secret.ISecretRepo

	harbor *models.Harbor
	log    *log.Helper
}

func NewTenantUsecase(
	nsK8sRepo namespace.INamespaceRepo,
	subnsK8sRepo namespace.ISubNamespaceRepo,
	hrqRepo quota.IHRQRepo,
	secretRepo secret.ISecretRepo,
	harborConfig *conf.Harbor,
	logger log.Logger) IUsecase {

	harbor := models.NewHarborFromPB(harborConfig)
	return &tenantUsecase{
		nsRepo:     nsK8sRepo,
		subnsRepo:  subnsK8sRepo,
		hrqRepo:    hrqRepo,
		secretRepo: secretRepo,
		harbor:     harbor,
		log:        log.NewHelper(logger),
	}
}

func (uc *tenantUsecase) CreateTenant(
	ctx context.Context, tenant *models.Tenant) (
	map[models.ResourcePoolType]string, error) {

	var err error
	uc.log.WithContext(ctx).Infof(
		"create tenant %s(%s)", tenant.Name, tenant.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"create tenant %s(%s) failed: %v", tenant.Name, tenant.Id, err)
		}
	}()

	// create k8s root ns, or subns for shared pool
	res := make(map[models.ResourcePoolType]string)
	k8sNsMap := tenant.ToK8sNamespaces()
	k8sSubNsMap := tenant.Tok8sSubNss()
	for pool, ns := range k8sNsMap {
		// create subns for shared pool
		if pool == models.ResourcePoolTypeShared {
			subns := k8sSubNsMap[pool]
			createdSubns, e := uc.subnsRepo.Create(ctx, subns)
			if err = e; err != nil {
				return nil, err
			}
			res[pool] = createdSubns.Name
			continue
		}

		// dedicated or reserved pool
		createdNs, e := uc.nsRepo.Create(ctx, ns)
		if err = e; err != nil {
			return nil, err
		}
		res[pool] = createdNs.Name
	}

	// create hrq if needed.
	hrqs := tenant.ToK8sHierarchicalQuotas()
	if len(hrqs) == 0 {
		// create default hrq for the tenant.
		hrqs = []*hnc.HierarchicalResourceQuota{
			models.NewZeroQuota(
				tenant.Id, "", models.ResourcePoolTypeDedicated).ToK8sHierarchicalQuota(),
			models.NewMaxQuota(
				tenant.Id, "", models.ResourcePoolTypeShared).ToK8sHierarchicalQuota(),
		}
	}
	for _, quota := range hrqs {
		_, err = uc.hrqRepo.Create(ctx, quota)
		if err != nil {
			return nil, err
		}
	}

	// create harbor secret
	for _, ns := range k8sNsMap {
		secret := uc.harbor.ToK8sSecret(ns.Name)
		_, err = uc.secretRepo.Create(ctx, secret)
		if err != nil {
			return nil, err
		}
	}

	uc.log.WithContext(ctx).Infof(
		"create tenant %s(%s) success", tenant.Name, tenant.Id)
	return res, nil
}

func (uc *tenantUsecase) RemoveTenant(
	ctx context.Context, tenant *models.Tenant) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"remove tenant %s(%s)", tenant.Name, tenant.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"remove tenant %s(%s) failed: %v",
				tenant.Name, tenant.Id, err)
		}
	}()

	// delete hrq firstly
	k8sNss := tenant.ToK8sNamespaces()
	for _, ns := range k8sNss {
		hrq := &hnc.HierarchicalResourceQuota{
			ObjectMeta: metav1.ObjectMeta{
				Name:      models.ResourceHierarchicalQuotaName,
				Namespace: ns.Name,
			},
		}
		err = uc.hrqRepo.Delete(ctx, hrq)
		if err != nil {
			return err
		}
	}

	// delete harbor secret
	for _, ns := range k8sNss {
		err = uc.secretRepo.Delete(ctx, &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      uc.harbor.SecretName,
				Namespace: ns.Name,
			},
		})
		if err != nil {
			return err
		}
	}

	// delete ns
	k8sSubnss := tenant.Tok8sSubNss()
	for pool, ns := range k8sNss {
		if pool == models.ResourcePoolTypeShared {
			subns := k8sSubnss[pool]
			err = uc.subnsRepo.Delete(ctx, subns)
			if err != nil {
				return err
			}
			continue
		}

		err = uc.nsRepo.Delete(ctx, ns)
		if err != nil {
			return err
		}
	}

	uc.log.WithContext(ctx).Infof(
		"remove tenant %s(%s) success", tenant.Name, tenant.Id)
	return nil
}

func (uc *tenantUsecase) UpdateTenant(
	ctx context.Context, tenant *models.Tenant) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"update tenant %s(%s)", tenant.Name, tenant.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"update tenant %s(%s) failed: %v", tenant.Name, tenant.Id, err)
		}
	}()

	// update meta info
	k8sNss := tenant.ToK8sNamespaces()
	k8sSubNss := tenant.Tok8sSubNss()
	for pool, ns := range k8sNss {
		if pool == models.ResourcePoolTypeShared {
			subns := k8sSubNss[pool]
			originK8s, e := uc.subnsRepo.Get(ctx, subns)
			if err = e; err != nil {
				return err
			}
			if originK8s == nil {
				err = errV1.ErrorKubernetesOperatorError(
					"subnamespace %s not found.", ns.Name)
				return err
			}

			err = models.MergeK8sSubNs(originK8s, subns)
			if err != nil {
				return err
			}

			_, err = uc.subnsRepo.Update(ctx, originK8s)
			if err != nil {
				return err
			}
			continue
		}

		originK8s, e := uc.nsRepo.Get(ctx, ns)
		if err = e; err != nil {
			return err
		}
		if originK8s == nil {
			err = errV1.ErrorKubernetesOperatorError(
				"namespace %s not found.", ns.Name)
			return err
		}

		err = models.MergeK8sNamespace(originK8s, ns)
		if err != nil {
			return err
		}

		_, err = uc.nsRepo.Update(ctx, originK8s)
		if err != nil {
			return err
		}
	}

	// update hrq if needed
	hrqs := tenant.ToK8sHierarchicalQuotas()
	for _, quota := range hrqs {
		k8sQuota, e := uc.hrqRepo.Get(ctx, quota)
		if err = e; err != nil {
			return err
		}
		if k8sQuota == nil {
			_, err = uc.hrqRepo.Create(ctx, quota)
			if err != nil {
				return err
			}
		} else {
			k8sQuota.Spec = quota.Spec
			_, err = uc.hrqRepo.Update(ctx, k8sQuota)
			if err != nil {
				return err
			}
		}
	}

	uc.log.WithContext(ctx).Infof(
		"update tenant %s(%s) success", tenant.Name, tenant.Id)
	return nil
}

func (uc *tenantUsecase) SyncTenant(
	ctx context.Context, tenant *models.Tenant) (
	map[models.ResourcePoolType]string, error) {

	var err error
	uc.log.WithContext(ctx).Infof(
		"sync tenant %s(%s)", tenant.Name, tenant.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"sync tenant %s(%s) failed: %v", tenant.Name, tenant.Id, err)
		}
	}()

	// sync meta info
	res := make(map[models.ResourcePoolType]string)
	k8sNss := tenant.ToK8sNamespaces()
	k8sSubNss := tenant.Tok8sSubNss()
	for pool, ns := range k8sNss {
		if pool == models.ResourcePoolTypeShared {
			subns := k8sSubNss[pool]
			originK8s, e := uc.subnsRepo.Get(ctx, subns)
			if err = e; err != nil {
				return nil, err
			}
			if originK8s == nil {
				createdSubns, e := uc.subnsRepo.Create(ctx, subns)
				if err = e; err != nil {
					return nil, err
				}
				res[pool] = createdSubns.Name
				continue
			}

			err = models.MergeK8sSubNs(originK8s, subns)
			if err != nil {
				return nil, err
			}

			_, err = uc.subnsRepo.Update(ctx, originK8s)
			if err != nil {
				return nil, err
			}
			res[pool] = originK8s.Name
			continue
		}

		originK8s, e := uc.nsRepo.Get(ctx, ns)
		if err = e; err != nil {
			return nil, err
		}
		if originK8s == nil {
			createdNs, e := uc.nsRepo.Create(ctx, ns)
			if err = e; err != nil {
				return nil, err
			}
			res[pool] = createdNs.Name
			continue
		}

		err = models.MergeK8sNamespace(originK8s, ns)
		if err != nil {
			return nil, err
		}

		_, err = uc.nsRepo.Update(ctx, originK8s)
		if err != nil {
			return nil, err
		}
		res[pool] = originK8s.Name
	}

	// sync hrqs
	hrqMap := tenant.ToK8sHierarchicalQuotaMap()
	for i := 1; i < int(models.ResourcePoolTypeCnt); i++ {
		poolType := models.ResourcePoolType(i)
		if poolType == models.ResourcePoolTypeReserved {
			continue
		}
		if _, ok := hrqMap[poolType]; ok {
			continue
		}

		// make other quotas zero or max
		var resetQuota *hnc.HierarchicalResourceQuota
		switch poolType {
		case models.ResourcePoolTypeShared:
			resetQuota = models.NewMaxQuota(
				tenant.Id, "", poolType).ToK8sHierarchicalQuota()
		case models.ResourcePoolTypeDedicated:
			fallthrough
		default:
			resetQuota = models.NewZeroQuota(
				tenant.Id, "", poolType).ToK8sHierarchicalQuota()
		}
		hrqMap[poolType] = resetQuota
	}

	// update hrq if needed
	for _, hrq := range hrqMap {
		k8sQuota, e := uc.hrqRepo.Get(ctx, hrq)
		if err = e; err != nil {
			return nil, err
		}
		if k8sQuota == nil {
			_, err = uc.hrqRepo.Create(ctx, hrq)
			if err != nil {
				return nil, err
			}
			continue
		}
		newQuota := k8sQuota.DeepCopy()
		newQuota.Spec = hrq.Spec
		_, err = uc.hrqRepo.Patch(ctx, k8sQuota, newQuota)
		if err != nil {
			return nil, err
		}
	}

	// sync harbor secret
	for _, ns := range k8sNss {
		secret := uc.harbor.ToK8sSecret(ns.Name)
		_, err = uc.secretRepo.Apply(ctx, secret)
		if err != nil {
			return nil, err
		}
	}

	uc.log.WithContext(ctx).Infof(
		"sync tenant %s(%s) success", tenant.Name, tenant.Id)
	return res, nil
}
