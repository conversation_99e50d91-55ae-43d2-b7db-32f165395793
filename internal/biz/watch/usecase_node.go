package watch

import (
	"context"

	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

func (uc *watchUsecase) nodeIdleOnTaskAdd(taskInfo *schedulingapi.TaskInfo) {
	if taskInfo.NodeName == "" {
		return
	}
	if !models.IsBizNamespace(taskInfo.Namespace) {
		return
	}

	nodeInfo := uc.watchRepo.GetNodeByName(taskInfo.NodeName)
	uc.checkNodeIdleAndSend(nodeInfo)
}

func (uc *watchUsecase) nodeIdleOnTaskUpdate(
	oldTaskInfo, newTaskInfo *schedulingapi.TaskInfo) {

	if !models.IsBizNamespace(newTaskInfo.Namespace) {
		return
	}
	if newTaskInfo.NodeName == "" && oldTaskInfo.NodeName == "" {
		return
	}
	if newTaskInfo.NodeName != "" && oldTaskInfo.NodeName == "" {
		nodeInfo := uc.watchRepo.GetNodeByName(newTaskInfo.NodeName)
		uc.checkNodeIdleAndSend(nodeInfo)
		return
	}
	if newTaskInfo.NodeName == "" && oldTaskInfo.NodeName != "" {
		nodeInfo := uc.watchRepo.GetNodeByName(oldTaskInfo.NodeName)
		uc.checkNodeIdleAndSend(nodeInfo)
		return
	}
	if newTaskInfo.NodeName == oldTaskInfo.NodeName {
		if newTaskInfo.Resreq.Equal(oldTaskInfo.Resreq, schedulingapi.Infinity) {
			return
		}
		nodeInfo := uc.watchRepo.GetNodeByName(newTaskInfo.NodeName)
		uc.checkNodeIdleAndSend(nodeInfo)
		return
	}

	// different node
	oldNodeInfo := uc.watchRepo.GetNodeByName(oldTaskInfo.NodeName)
	uc.checkNodeIdleAndSend(oldNodeInfo)
	newNodeInfo := uc.watchRepo.GetNodeByName(newTaskInfo.NodeName)
	uc.checkNodeIdleAndSend(newNodeInfo)
}

func (uc *watchUsecase) nodeIdleOnTaskDelete(taskInfo *schedulingapi.TaskInfo) {
	if taskInfo.NodeName == "" {
		return
	}
	if !models.IsBizNamespace(taskInfo.Namespace) {
		return
	}

	nodeInfo := uc.watchRepo.GetNodeByName(taskInfo.NodeName)
	uc.checkNodeIdleAndSend(nodeInfo)
}

func (uc *watchUsecase) checkNodeIdleAndSend(nodeInfo *schedulingapi.NodeInfo) {
	if nodeInfo == nil {
		uc.log.Warnf("nodeInfo is nil when checkNodeIdleAndSend.")
		return
	}
	if nodeInfo.Node == nil {
		uc.log.Errorf("k8s node of %s is nil when checkNodeIdleAndSend, maybe notReady.",
			nodeInfo.Name)
		return
	}
	if nodeInfo.Node.UID == "" {
		uc.log.Errorf("node %s uid is empty when checkNodeIdleAndSend.", nodeInfo.Name)
		return
	}

	idle := models.IsNodeInfoBizEmpty(nodeInfo)

	uc.nodeLock.Lock()
	defer uc.nodeLock.Unlock()
	cacheIdel, cached := uc.nodeIdleMap[nodeInfo.Name]
	if cached && cacheIdel == idle {
		uc.log.Infof("node %s idle state not changed.", nodeInfo.Name)
		return
	}

	uc.nodeIdleMap[nodeInfo.Name] = idle
	msg := tecmq.NodeMsgWorkingStateChanged{
		NodeUUID: string(nodeInfo.Node.UID),
		Idle:     idle,
	}
	err := uc.mqRepo.Public(
		context.TODO(), tecons.NatsSubjectNodeWorkingStateChanged, msg)
	if err != nil {
		uc.log.Errorf(
			"publish message %+v to %s failed: %v",
			msg, tecons.NatsSubjectNodeWorkingStateChanged, err)
		return
	}
	uc.log.Infof(
		"publish message %+v to %s success.",
		msg, tecons.NatsSubjectNodeWorkingStateChanged)
}
