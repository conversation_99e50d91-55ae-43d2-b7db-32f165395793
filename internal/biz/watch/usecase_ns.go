package watch

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

func (uc *watchUsecase) nsUsageOnTaskAdd(taskInfo *schedulingapi.TaskInfo) {
	if !models.IsBizNamespace(taskInfo.Namespace) {
		return
	}
	if !schedulingapi.AllocatedStatus(taskInfo.Status) {
		return
	}
	if taskInfo.NodeName == "" {
		return
	}
	tenantID, workspaceID, poolType, cardModel, err := uc.getTaskMetaInfo(taskInfo)
	if err != nil {
		uc.log.Errorf("failed to get task %s/%s meta info, retry after %d seconds: %v",
			taskInfo.Namespace, taskInfo.Name, models.NodeInfoRetrySeconds/time.Second, err)
		time.AfterFunc(models.NodeInfoRetrySeconds, func() {
			uc.nsUsageOnTaskAdd(taskInfo)
		})
		return
	}

	uc.log.Infof("Stat namespace usage when add task %s/%s, tenantID: %s, workspaceID: %s, poolType: %s, cardModel: %s",
		taskInfo.Namespace, taskInfo.Name, tenantID, workspaceID, poolType, cardModel)
	// 计算资源使用量
	usage := models.NewQuotaTypedUsageFromResreq(taskInfo.Resreq, cardModel)

	// 更新缓存并发送消息
	uc.updateNsUsageAndSend(models.TenantSegsInK8sNs, cast.ToUint64(tenantID), poolType, usage)
	uc.updateNsUsageAndSend(models.WorkspaceSegsInK8sNs, cast.ToUint64(workspaceID), poolType, usage)
}

func (uc *watchUsecase) nsUsageOnTaskUpdate(
	oldTaskInfo, newTaskInfo *schedulingapi.TaskInfo) {

	if !models.IsBizNamespace(newTaskInfo.Namespace) {
		return
	}
	if newTaskInfo.NodeName == "" && oldTaskInfo.NodeName == "" {
		return
	}

	oldAllocated := schedulingapi.AllocatedStatus(oldTaskInfo.Status)
	newAllocated := schedulingapi.AllocatedStatus(newTaskInfo.Status)
	if !newAllocated && !oldAllocated {
		return
	}
	if newAllocated && !oldAllocated {
		uc.nsUsageOnTaskAdd(newTaskInfo)
		return
	}
	if !newAllocated && oldAllocated {
		uc.log.Infof("Stat namespace usage when update task %s/%s, oldTaskInfo: %v",
			oldTaskInfo.Namespace, oldTaskInfo.Name, oldTaskInfo)
		uc.nsUsageOnTaskDelete(oldTaskInfo)
		return
	}

	// both allocated, compare usage
	oldUsage := models.NewQuotaTypedUsageFromResreq(oldTaskInfo.Resreq, models.CardModel_Unknown)
	newUsage := models.NewQuotaTypedUsageFromResreq(newTaskInfo.Resreq, models.CardModel_Unknown)
	if oldUsage.Equal(newUsage) {
		return
	}

	uc.log.Infof("Stat namespace usage when update task %s/%s, oldUsage: %s, newUsage: %s",
		newTaskInfo.Namespace, newTaskInfo.Name, oldUsage, newUsage)
	// no equal, should not happen in k8s
	uc.nsUsageOnTaskDelete(oldTaskInfo)
	uc.nsUsageOnTaskAdd(newTaskInfo)
}

func (uc *watchUsecase) nsUsageOnTaskDelete(taskInfo *schedulingapi.TaskInfo) {
	if !models.IsBizNamespace(taskInfo.Namespace) {
		return
	}
	if taskInfo.NodeName == "" {
		return
	}
	if !schedulingapi.AllocatedStatus(taskInfo.Status) {
		return
	}
	tenantID, workspaceID, poolType, cardModel, err := uc.getTaskMetaInfo(taskInfo)
	if err != nil {
		uc.log.Errorf("failed to get task %s/%s meta info, retry after %d seconds: %v",
			taskInfo.Namespace, taskInfo.Name, models.NodeInfoRetrySeconds/time.Second, err)
		time.AfterFunc(models.NodeInfoRetrySeconds, func() {
			uc.nsUsageOnTaskDelete(taskInfo)
		})
		return
	}

	uc.log.Infof("Stat namespace usage when delete task %s/%s, tenantID: %s, workspaceID: %s, poolType: %s, cardModel: %s",
		taskInfo.Namespace, taskInfo.Name, tenantID, workspaceID, poolType, cardModel)
	// 计算要减少的资源使用量
	addUsage := models.NewEmptyQuotaTypedUsage()
	usage := models.NewQuotaTypedUsageFromResreq(taskInfo.Resreq, cardModel)
	addUsage.Sub(usage)

	// 更新缓存并发送消息
	uc.updateNsUsageAndSend(models.TenantSegsInK8sNs, cast.ToUint64(tenantID), poolType, addUsage)
	uc.updateNsUsageAndSend(models.WorkspaceSegsInK8sNs, cast.ToUint64(workspaceID), poolType, addUsage)
}

func (uc *watchUsecase) updateNsUsageAndSend(
	hierarchy string, id uint64, poolType models.ResourcePoolType,
	usage *models.QuotaTypedUsage) {

	uc.nsLock.Lock()
	defer uc.nsLock.Unlock()

	// 获取或创建租户的资源池使用量映射
	var targetMap map[uint64]map[models.ResourcePoolType]*models.QuotaTypedUsage
	switch hierarchy {
	case models.TenantSegsInK8sNs:
		targetMap = uc.tenantUsage
	case models.WorkspaceSegsInK8sNs:
		targetMap = uc.workspaceUsage
	}
	poolUsageMap, exists := targetMap[id]
	if !exists {
		poolUsageMap = make(map[models.ResourcePoolType]*models.QuotaTypedUsage)
		targetMap[id] = poolUsageMap
	}

	// 获取或创建资源池的使用量
	currentUsage, exists := poolUsageMap[poolType]
	if !exists {
		currentUsage = models.NewEmptyQuotaTypedUsage()
		poolUsageMap[poolType] = currentUsage
	}

	// 更新使用量
	currentUsage.Add(usage)

	// 发送消息
	var topic string
	var msg interface{}
	switch hierarchy {
	case models.TenantSegsInK8sNs:
		if poolType == models.ResourcePoolTypeDedicated {
			topic = tecons.NatsSubjectRMTenantQuotaUsageChanged
		} else {
			topic = tecons.NatsSubjectRMTenantOnDemandQuotaUsageChanged
		}
		msg = &tecmq.RMMsgTenantQuota{
			TenantID: id,
			Quotas:   currentUsage.ToQuotaItems(),
		}
	case models.WorkspaceSegsInK8sNs:
		if poolType == models.ResourcePoolTypeDedicated {
			topic = tecons.NatsSubjectRMWorkspaceQuotaUsageChanged
		} else {
			topic = tecons.NatsSubjectRMWorkspaceOnDemandQuotaUsageChanged
		}
		msg = &tecmq.RMMsgWorkspaceQuota{
			WorkspaceID: id,
			Quotas:      currentUsage.ToQuotaItems(),
		}
	default:
		uc.log.Errorf("unsupported resource pool type: %s", poolType)
		return
	}

	err := uc.mqRepo.Public(context.TODO(), topic, msg)
	if err != nil {
		uc.log.Errorf("failed to publish message to %s: %v", topic, err)
		return
	}
	uc.log.Infof("successfully published message to %s: %+v", topic, msg)
}

func (uc *watchUsecase) getTaskMetaInfo(
	taskInfo *schedulingapi.TaskInfo) (
	tenantID, workspaceID string, poolType models.ResourcePoolType,
	cardModel models.CardModel, err error) {

	// 获取租户ID, workspaceID, 资源池类型
	tenantStr, ok := taskInfo.Pod.Labels[models.PodTenantLabelKey]
	if !ok {
		err = fmt.Errorf("failed to get teannt from pod labels.")
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	_, tenantID, _, err = models.ParseK8sNamespace(tenantStr)
	if err != nil {
		err = fmt.Errorf("failed to parse k8s namespace %s: %v", tenantStr, err)
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	workspaceStr, ok := taskInfo.Pod.Labels[models.PodWorkspaceLabelKey]
	if !ok {
		err = fmt.Errorf("failed to get workspace from pod labels.")
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	_, workspaceID, _, err = models.ParseK8sNamespace(workspaceStr)
	if err != nil {
		err = fmt.Errorf("failed to parse k8s namespace %s: %v", workspaceStr, err)
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	resourceModeStr := taskInfo.Pod.Labels[models.PodResourceModeLabelKey]
	poolType = models.ResourceMode2ResourcePoolType(resourceModeStr)
	if poolType == models.ResourcePoolTypeUnknown {
		log.Warnf("unkown resource type str: %s, try to get pool type from namespace", resourceModeStr)
		// try to get pool type from namespace
		_, _, poolType, err = models.ParseK8sNamespace(taskInfo.Namespace)
		if err != nil {
			err = fmt.Errorf("failed to parse k8s namespace %s: %v", taskInfo.Namespace, err)
			uc.log.Errorf(err.Error())
			return tenantID, workspaceID, poolType, cardModel, err
		}
	}
	if poolType == models.ResourcePoolTypeUnknown {
		err = fmt.Errorf("unknown resource pool type in getTaskMetaInfo %s/%s: %s",
			taskInfo.Namespace, taskInfo.Name, resourceModeStr)
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	nodeInfo := uc.watchRepo.GetNodeByName(taskInfo.NodeName)
	if nodeInfo == nil {
		err = fmt.Errorf("failed to get node info for node %s", taskInfo.NodeName)
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	if nodeInfo.Node == nil {
		err = fmt.Errorf("failed to get k8s node for node info %s", taskInfo.NodeName)
		uc.log.Errorf(err.Error())
		return tenantID, workspaceID, poolType, cardModel, err
	}
	cardModel = models.ParseCardModelFromMap(nodeInfo.Node.Labels)
	quotaValue := models.NewQuotaValueFromResource(taskInfo.Resreq)
	tecoReq := quotaValue.ExtendedResource[models.ExtendedResourceTeco]
	if cardModel.ExtendedResourceType() == models.ExtendedResourceTeco &&
		tecoReq > 0 {
		if tecoReq < uint64(cardModel.Cores()) {
			cardModel = models.CardModel_Unknown
		}
	}
	return tenantID, workspaceID, poolType, cardModel, nil
}
