package watch

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	corev1 "k8s.io/api/core/v1"
	k8sCache "k8s.io/client-go/tools/cache"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"
)

func (uc *watchUsecase) taskHandler() k8sCache.ResourceEventHandler {

	addFunc := func(obj interface{}) {
		taskInfo := obj.(*schedulingapi.TaskInfo)
		go uc.nodeIdleOnTaskAdd(taskInfo)
		go uc.nsUsageOnTaskAdd(taskInfo)
	}

	updateFunc := func(oldObj, newObj interface{}) {
		oldTaskInfo := oldObj.(*schedulingapi.TaskInfo)
		newTaskInfo := newObj.(*schedulingapi.TaskInfo)
		go uc.nodeIdleOnTaskUpdate(oldTaskInfo, newTaskInfo)
		go uc.nsUsageOnTaskUpdate(oldTaskInfo, newTaskInfo)
	}

	deleteFunc := func(obj interface{}) {
		taskInfo := obj.(*schedulingapi.TaskInfo)
		go uc.nodeIdleOnTaskDelete(taskInfo)
		go uc.nsUsageOnTaskDelete(taskInfo)
	}

	return k8sCache.ResourceEventHandlerFuncs{
		AddFunc:    addFunc,
		UpdateFunc: updateFunc,
		DeleteFunc: deleteFunc,
	}
}

func (uc *watchUsecase) nodeHandler() k8sCache.ResourceEventHandler {

	addFunc := func(obj interface{}) {
		nodeInfo := obj.(*schedulingapi.NodeInfo)
		uc.checkNodeIdleAndSend(nodeInfo)
	}

	updateFunc := func(oldObj, newObj interface{}) {
		nodeInfo := newObj.(*schedulingapi.NodeInfo)
		uc.checkNodeIdleAndSend(nodeInfo)
	}

	return k8sCache.ResourceEventHandlerFuncs{
		AddFunc:    addFunc,
		UpdateFunc: updateFunc,
		DeleteFunc: nil,
	}
}

func (uc *watchUsecase) hrqHandler(
	ctx context.Context) k8sCache.ResourceEventHandler {

	addFunc := func(obj interface{}) {
		hrq := obj.(*hnc.HierarchicalResourceQuota)
		topic, msg, err := models.HrqToQuotaUsageMsg(hrq)
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"format msg failed when publish message on addHrq %s/%s to %s: %v",
				hrq.Namespace, hrq.Name, topic, err)
			return
		}
		err = uc.mqRepo.Public(ctx, topic, msg)
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"publish message on addHrq %+v to %s failed: %v",
				msg, topic, err)
			return
		}
		uc.log.WithContext(ctx).Infof(
			"publish message on addHrq %+v to %s success.", msg, topic)
	}

	updateFunc := func(oldObj, newObj interface{}) {
		oldHrq := oldObj.(*hnc.HierarchicalResourceQuota)
		hrq := newObj.(*hnc.HierarchicalResourceQuota)
		changed := models.ResourceListChanged(
			oldHrq.Status.Used, hrq.Status.Used)
		if !changed {
			uc.log.WithContext(ctx).Infof(
				"hrq %s/%s used not changed onUpdate.",
				hrq.Namespace, hrq.Name)
			return
		}

		topic, msg, err := models.HrqToQuotaUsageMsg(hrq)
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"format msg failed when publish message on updateHrq %s/%s to %s: %v",
				hrq.Namespace, hrq.Name, topic, err)
			return
		}
		err = uc.mqRepo.Public(ctx, topic, msg)
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"publish message on updateHrq %+v to %s failed: %v",
				msg, topic, err)
			return
		}
		uc.log.WithContext(ctx).Infof(
			"publish message on updateHrq %+v to %s success.", msg, topic)
	}

	return k8sCache.ResourceEventHandlerFuncs{
		AddFunc:    addFunc,
		UpdateFunc: updateFunc,
		DeleteFunc: nil,
	}
}

func (uc *watchUsecase) quotaHandler(
	ctx context.Context) k8sCache.ResourceEventHandler {

	return k8sCache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			quota := obj.(*corev1.ResourceQuota)
			topic, msg, err := models.K8sQuotaToQuotaUsageMsg(quota)
			if err != nil {
				uc.log.WithContext(ctx).Errorf(
					"format msg failed when publish message on addQuota %s/%s to %s: %v",
					quota.Namespace, quota.Name, topic, err)
				return
			}
			if topic == tecons.NatsSubjectRMTenantQuotaUsageChanged {
				// tenant usage informed in hrq.
				return
			}
			err = uc.mqRepo.Public(ctx, topic, msg)
			if err != nil {
				uc.log.WithContext(ctx).Errorf(
					"publish message on addQuota %+v to %s failed: %v",
					msg, topic, err)
				return
			}
			uc.log.WithContext(ctx).Infof(
				"publish message on addQuota %+v to %s success.", msg, topic)
		},

		UpdateFunc: func(oldObj, newObj interface{}) {
			oldQuota := oldObj.(*corev1.ResourceQuota)
			quota := newObj.(*corev1.ResourceQuota)
			changed := models.ResourceListChanged(
				oldQuota.Status.Used, quota.Status.Used)
			if !changed {
				uc.log.WithContext(ctx).Infof(
					"quota %s/%s used not changed onUpdate.",
					quota.Namespace, quota.Name)
				return
			}

			topic, msg, err := models.K8sQuotaToQuotaUsageMsg(quota)
			if err != nil {
				uc.log.WithContext(ctx).Errorf(
					"format msg failed when publish message on updateQuota %s/%s to %s: %v",
					quota.Namespace, quota.Name, topic, err)
				return
			}

			if topic == tecons.NatsSubjectRMTenantQuotaUsageChanged {
				// tenant usage infored in hrq.
				return
			}

			err = uc.mqRepo.Public(ctx, topic, msg)
			if err != nil {
				uc.log.WithContext(ctx).Errorf(
					"publish message on updateQuota %+v to %s failed: %v",
					msg, topic, err)
				return
			}
			uc.log.WithContext(ctx).Infof(
				"publish message on updateQuota %+v to %s success.", msg, topic)
		},

		DeleteFunc: nil,
	}
}
