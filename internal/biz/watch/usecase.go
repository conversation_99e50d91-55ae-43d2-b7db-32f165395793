package watch

import (
	"context"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/robfig/cron/v3"

	cache "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type watchUsecase struct {
	// nodeName -> idle
	nodeIdleMap map[string]bool
	nodeLock    sync.Mutex

	// pool(id) -> usage
	poolUsage map[string]*models.QuotaTypedUsage

	// ns(id) -> poolType -> usage
	tenantUsage    map[uint64]map[models.ResourcePoolType]*models.QuotaTypedUsage
	workspaceUsage map[uint64]map[models.ResourcePoolType]*models.QuotaTypedUsage
	nsLock         sync.Mutex

	watchRepo cache.IWatchRepository
	mqRepo    cache.IMQRepository

	log *log.Helper
}

func NewWatchUsecase(
	k8sWatchRepo cache.IWatchRepository, mqRepo cache.IMQRepository,
	logger log.Logger) IUsecase {

	return &watchUsecase{
		nodeIdleMap:    map[string]bool{},
		poolUsage:      map[string]*models.QuotaTypedUsage{},
		tenantUsage:    map[uint64]map[models.ResourcePoolType]*models.QuotaTypedUsage{},
		workspaceUsage: map[uint64]map[models.ResourcePoolType]*models.QuotaTypedUsage{},
		watchRepo:      k8sWatchRepo,
		mqRepo:         mqRepo,
		log:            log.NewHelper(logger),
	}
}

func (uc *watchUsecase) Init() error {

	var err error
	ctx := context.TODO()
	uc.log.WithContext(ctx).Infof("init watch module")
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf("init watch failed: %v", err)
		}
	}()

	// 1. watch k8s resources
	// register node handlers
	uc.watchRepo.RegisterNodeHandler(uc.nodeHandler())

	// register task handlers
	uc.watchRepo.RegisterTaskHandler(uc.taskHandler())

	// register hrq handlers
	// uc.watchRepo.RegisterHrqHandler(uc.hrqHandler(ctx))

	// registger quota handlers.
	// uc.watchRepo.RegisterQuotaHandler(uc.quotaHandler(ctx))

	// start watch
	stopCh := make(<-chan struct{})
	if err = uc.watchRepo.Run(stopCh); err != nil {
		return err
	}

	// 2. start pool usage sync.
	// starte the cronjob to sync the pool usage.
	c := cron.New(cron.WithChain(cron.SkipIfStillRunning(cron.DefaultLogger)))
	_, err = c.AddFunc("*/1 * * * *", uc.syncPoolUsage)
	if err != nil {
		log.Fatalf("Add pool usage sync routine failed: %v", err)
	}
	c.Start()

	// 3. exit init.
	uc.log.WithContext(ctx).Infof("init watch module success.")
	return nil
}
