package watch

import (
	"context"

	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"
	"volcano.sh/volcano/pkg/scheduler/api/devices/teco"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

func (uc *watchUsecase) sendPoolUsage(
	poolId string, poolType models.ResourcePoolType, usage *models.QuotaTypedUsage) {

	if poolId == "" || usage == nil {
		uc.log.Errorf("pool is empty or usage is nil when sendPoolUsage.")
		return
	}
	if poolType != models.ResourcePoolTypeShared {
		return
	}

	msg := tecmq.PoolMsgPoolUsage{
		PoolUUID: poolId,
		Usage:    usage.ToQuotaItems(),
	}
	err := uc.mqRepo.Public(
		context.TODO(), tecons.NatsSubjectPoolUsageChanged, msg)
	if err != nil {
		uc.log.Errorf(
			"publish message %+v to %s failed: %v, retry later.",
			msg, tecons.NatsSubjectPoolUsageChanged, err)
		return
	}
	uc.log.Infof(
		"publish message %+v to %s success.",
		msg, tecons.NatsSubjectPoolUsageChanged)
}

func (uc *watchUsecase) syncPoolUsage() {
	uc.log.Info("Start sync pool usage...")

	// 1. get all nodes
	nodes := uc.watchRepo.ListNodesByLabels(nil)

	// 2. iterate all nodes format pool usage
	poolUsage := make(map[string]*models.QuotaTypedUsage)
	for _, ni := range nodes {
		if ni == nil || ni.Node == nil {
			uc.log.Warnf(
				"node(%s) not found while sync pool usage, skip!", ni.Node.Name)
			continue
		}
		poolType := models.ParsePoolTypeFromMap(ni.Node.Labels)
		if poolType != models.ResourcePoolTypeShared {
			uc.log.Warnf(
				"pool type %s for node(%s) neglected while sync pool usage, skip!",
				poolType.String(), ni.Node.Name)
			continue
		}
		poolId := models.ParsePoolIdFromMap(ni.Node.Labels)
		if poolId == "" {
			uc.log.Warnf(
				"pool id not found for node(%s) while sync pool usage, skip!",
				ni.Node.Name)
			continue
		}

		nodeUsage := uc.sumNodeUsage(ni)
		uc.adjustNodeUsageOnTeco(nodeUsage, ni)
		if _, ok := poolUsage[poolId]; !ok {
			poolUsage[poolId] = nodeUsage
		} else {
			poolUsage[poolId].Add(nodeUsage)
		}
	}

	// 3. compare with the pool usage cache, and send if needed.
	for poolId, usage := range poolUsage {
		if originUsage, ok := uc.poolUsage[poolId]; !ok {
			uc.poolUsage[poolId] = usage
			uc.sendPoolUsage(poolId, models.ResourcePoolTypeShared, usage)
		} else {
			// compare and send if needed.
			if usage.Equal(originUsage) {
				continue
			}
			uc.poolUsage[poolId] = usage
			uc.sendPoolUsage(poolId, models.ResourcePoolTypeShared, usage)
		}
	}
	for poolId := range uc.poolUsage {
		if _, ok := poolUsage[poolId]; !ok {
			delete(uc.poolUsage, poolId)
			uc.sendPoolUsage(poolId, models.ResourcePoolTypeShared,
				models.NewEmptyQuotaTypedUsage())
		}
	}
}

func (uc *watchUsecase) sumNodeUsage(
	ni *schedulingapi.NodeInfo) *models.QuotaTypedUsage {

	res := models.NewEmptyQuotaTypedUsage()
	if ni == nil {
		return res
	}

	model := models.ParseCardModelFromMap(ni.Node.Labels)
	for _, taskInfo := range ni.Tasks {
		if taskInfo.NodeName == "" {
			continue
		}
		if !models.IsBizNamespace(taskInfo.Namespace) {
			continue
		}
		if !schedulingapi.AllocatedStatus(taskInfo.Status) {
			continue
		}
		res.Add(models.NewQuotaTypedUsageFromResreq(taskInfo.Resreq, model))
	}
	return res
}

func (uc *watchUsecase) adjustNodeUsageOnTeco(
	usage *models.QuotaTypedUsage, ni *schedulingapi.NodeInfo) {

	model := models.ParseCardModelFromMap(ni.Node.Labels)
	if model.ExtendedResourceType() != models.ExtendedResourceTeco {
		return
	}
	tecoDevices := ni.Others[teco.DeviceName].(*teco.TecoDevices)
	if tecoDevices == nil {
		return
	}

	cnt := 0
	for _, device := range tecoDevices.Device {
		if device.UsedNum > 0 {
			cnt++
		}
	}
	usage.ExtendedResource[models.ExtendedResourceTeco][model] = uint64(cnt)
}
