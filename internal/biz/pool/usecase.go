package pool

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	quotav1 "k8s.io/apiserver/pkg/quota/v1"
	"volcano.sh/volcano/pkg/scheduler/api"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	namespace "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/pool"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	cache "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type poolUsecase struct {
	rpcRepo   pool.IRrpcRepository
	watchRepo cache.IWatchRepository
	nsRepo    namespace.INamespaceRepo
	hrqRepo   quota.IHRQRepo

	log *log.Helper
}

func NewUsecase(
	repo pool.IRrpcRepository, watchRepo cache.IWatchRepository,
	nsRepo namespace.INamespaceRepo, hrqRepo quota.IHRQRepo,
	logger log.Logger) IUsecase {

	return &poolUsecase{
		rpcRepo:   repo,
		watchRepo: watchRepo,
		nsRepo:    nsRepo,
		hrqRepo:   hrqRepo,
		log:       log.NewHelper(logger),
	}
}

func (uc *poolUsecase) Init() error {
	ctx := context.TODO()

	// create shared ns.
	sharedNs := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: models.ResourcePoolTypeSharedK8sNs,
			Labels: map[string]string{
				models.HncPoolLabel: models.ResourcePoolTypeShared.String(),
			},
		},
	}
	originNs, err := uc.nsRepo.Get(ctx, sharedNs)
	if err != nil {
		return err
	}
	if originNs != nil {
		uc.log.Infof("shared ns %s exist.", models.ResourcePoolTypeSharedK8sNs)
	}
	if originNs == nil {
		if _, err = uc.nsRepo.Create(ctx, sharedNs); err != nil {
			return err
		}
		uc.log.Infof("init shared ns %s success.",
			models.ResourcePoolTypeSharedK8sNs)
	}

	// create hrq.
	hrqs, err := uc.hrqRepo.List(ctx,
		models.ResourcePoolTypeSharedK8sNs, labels.Everything())
	if err != nil {
		return err
	}
	if len(hrqs) > 0 {
		uc.log.Infof("hrq for the shared ns %s exist.", hrqs[0].Name)
	}
	if len(hrqs) == 0 {
		quota := models.NewMaxQuota("", "", models.ResourcePoolTypeShared)
		hrq := quota.ToK8sHierarchicalQuota()
		hrq.Namespace = models.ResourcePoolTypeSharedK8sNs
		_, err = uc.hrqRepo.Create(ctx, hrq)
		if err != nil {
			return err
		}
		uc.log.Infof("init hrq %s for the shared ns success.",
			models.ResourceHierarchicalQuotaName)
	}
	return nil
}

// ResourceAvailable 检查资源池中是否有可用资源
// 返回值：
// available: 可用资源数量
// reason: 资源限制原因
// keyResources: 关键资源
// detail: 详细描述
// 错误：nil  或 错误信息
func (uc *poolUsecase) ResourceAvailable(
	ctx context.Context, pool models.ResourcePoolType,
	workspace *models.Workspace, specId string, usage *models.QuotaTypedUsage) (
	int64, models.ResourceLimitReason, []string, string, error) {

	var err error
	uc.log.WithContext(ctx).Infof(
		"resource available check in pool: %s, workspace: %s, specId: %s, detail: %v",
		pool.String(), workspace.Id, specId, usage)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"resource available check failed in pool: %s, workspace: %s, specId: %s, detail: %v, err: %v",
				pool.String(), workspace.Id, specId, usage, err)
		}
	}()

	quotaValue := usage.ToQuotaValue()
	if quotaValue.MultiER() {
		err = errV1.ErrorInvalidParams(
			"multi extended resource not supported in one instance.")
		return 0, models.ResourceLimitReason_Unknown, nil, "", err
	}

	// 0. quota check
	ns := models.GetK8sNamespace(pool, models.WorkspaceSegsInK8sNs, workspace.Id)
	hrq := uc.watchRepo.GetNamespaceHrq(ctx, ns)
	if hrq == nil {
		err = errV1.ErrorQuotaNotFound("hrq of namespace %s not found.", ns)
		return 0, models.ResourceLimitReason_Unknown, nil, "", err
	}
	quota := uc.watchRepo.GetNamespaceQuota(ctx, ns)
	if quota == nil {
		err = errV1.ErrorQuotaNotFound("quota of namespace %s not found.", ns)
		return 0, models.ResourceLimitReason_Unknown, nil, "", err
	}
	used := hrq.QuotaStatus[models.ResourceHierarchicalQuotaName].Used
	hard := quota.QuotaStatus[models.HncResourceQuotaName].Hard
	available := quotav1.SubtractWithNonNegativeResult(hard, used)
	qvAvailable := models.NewQuotaValueFromK8sResourceList(available)
	quotaAvailableCnt, limitedResource := qvAvailable.Divide(quotaValue)
	uc.log.WithContext(ctx).Infof(
		"available resource by quota: %v, key resource is %s.",
		quotaAvailableCnt, limitedResource)
	if quotaAvailableCnt == 0 {
		detail := fmt.Sprintf("quota not enough, limited by %s.", limitedResource)
		return 0, models.ResourceLimitReason_Quota, []string{limitedResource}, detail, nil
	}

	// 1. node check
	// 1.1 get nodes range maybe allocated.
	nodesInfo := make(map[types.UID]*api.NodeInfo)
	switch pool {
	case models.ResourcePoolTypeShared:
		nodes, err := uc.rpcRepo.ListNodesBySpec(ctx, specId)
		if err != nil {
			return 0, models.ResourceLimitReason_Unknown, nil, "", err
		}
		uids := sets.New[types.UID]()
		for _, node := range nodes {
			uids.Insert(types.UID(node.Id))
		}
		uc.log.WithContext(ctx).Infof("spec qualified node uids: %v", uids)
		nodesInfo = uc.watchRepo.GetNodeByUIDs(uids)
		if len(nodesInfo) != len(nodes) {
			foundUIDs := sets.New[types.UID]()
			for uid := range nodesInfo {
				foundUIDs.Insert(uid)
			}
			notFoundUIDs := uids.Difference(foundUIDs)
			uc.log.WithContext(ctx).Warnf(
				"some nodes info not found, want %v, get %v, not found %v",
				len(nodes), len(nodesInfo), notFoundUIDs)
		}
	case models.ResourcePoolTypeDedicated:
		nodes := uc.watchRepo.ListNodesByLabels(
			map[string]string{
				models.ResourcePoolLabelKey:        pool.String(),
				models.NodeDedicatedTenantLabelKey: workspace.TenantId,
			})
		for _, node := range nodes {
			nodesInfo[node.Node.UID] = node
		}
	default:
		err = errV1.ErrorInvalidParams(
			"unsupported resource pool type now: %s.", pool.String())
		return 0, models.ResourceLimitReason_Unknown, nil, "", err
	}

	// 1.2 get nodes available resource cnt
	nodeAvailableCnt := int64(0)
	details := []string{}
	limitRscs := []string{}
	for _, node := range nodesInfo {
		if !node.Ready() {
			details = append(details, fmt.Sprintf(
				"%s limited %d by %s", node.Name, 0, "NotReady"))
			continue
		}

		cardModel := models.ParseCardModelFromMap(node.Node.Labels)
		if usage.IsTecoCoreUsage() {
			cardModel = models.CardModel_Unknown
		}
		// which resource is the bottleneck
		typedIdle := models.NewQuotaTypedUsageFromResreq(node.Idle, cardModel)
		if !usage.IsTecoCoreUsage() {
			// filter fragment resource
			idleCards := models.NodeIdleDivideTecoCoreReq(node, uint(cardModel.Cores()))
			typedIdle.ExtendedResource[models.ExtendedResourceTeco][cardModel] =
				uint64(idleCards) * uint64(cardModel.Cores())
		}
		resCnt, limitRsc := typedIdle.Divide(usage)
		if usage.IsTecoCoreUsage() {
			coreReq := usage.ExtendedResource[models.ExtendedResourceTeco][models.CardModel_Unknown]
			coreReqCnt := int64(models.NodeIdleDivideTecoCoreReq(node, uint(coreReq)))
			if coreReqCnt < resCnt {
				resCnt = coreReqCnt
				limitRsc = "tecocore"
			}
		}
		// check pod cnt
		podCnt := int64(node.Idle.Get(corev1.ResourcePods))
		if resCnt >= podCnt {
			nodeAvailableCnt += podCnt
			limitRscs = append(limitRscs, "pod")
			details = append(details, fmt.Sprintf(
				"%s limited %d by %s", node.Name, podCnt, "pod"))
		} else {
			nodeAvailableCnt += resCnt
			limitRscs = append(limitRscs, limitRsc)
			details = append(details, fmt.Sprintf(
				"%s limited %d by %s", node.Name, resCnt, limitRsc))
		}
	}
	uc.log.WithContext(ctx).Infof(
		"available resource by nodes: %d, details: %s",
		nodeAvailableCnt, strings.Join(details, ", "))

	// 3. return result
	if nodeAvailableCnt < quotaAvailableCnt {
		detail := fmt.Sprintf(
			"available limited by node resource, only %d. details: %s",
			nodeAvailableCnt, strings.Join(details, ", "))
		return nodeAvailableCnt, models.ResourceLimitReason_Pool, limitRscs, detail, nil
	}

	detail := fmt.Sprintf(
		"available limited by quota, only %d. key resource is %s",
		quotaAvailableCnt, limitedResource)
	return quotaAvailableCnt, models.ResourceLimitReason_Quota,
		[]string{limitedResource}, detail, nil
}
