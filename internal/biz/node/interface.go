package node

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type IUsecase interface {
	Init() error
	AllocateNodeToTenant(
		context.Context, *models.Node, *models.Tenant) error
	DeallocateNodeToTenant(
		context.Context, *models.Node, *models.Tenant) error
	AllocateNodeToWorkspace(
		context.Context, *models.Node, *models.Workspace) error
	DeallocateNodeToWorkspace(
		context.Context, *models.Node, *models.Workspace) error
	AllocateNodeToResourcePool(
		context.Context, *models.Node, models.ResourcePoolType, string) error
	DeallocateNodeToResourcePool(
		context.Context, *models.Node, models.ResourcePoolType, string) error
}
