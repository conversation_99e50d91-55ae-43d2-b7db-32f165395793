package node

import (
	"context"
	"encoding/json"
	"strconv"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	billingPb "git-plat.tecorigin.net/ai-platform/billing-server/api/billing/v1"
	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/node"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	cache "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/nats-io/nats.go/jetstream"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

const ConsumerName = tecons.NatsConsumerPrefixSchedulingManager + "node"

type nodeUsecase struct {
	nodeRepo  node.IRepository
	nsRepo    namespace.INamespaceRepo
	hrqRepo   quota.IHRQRepo
	mqRepo    node.IMQRepository
	watchRepo cache.IWatchRepository

	log *log.Helper
}

func NewNodeUsecase(
	nodeK8sRepo node.IRepository, nsK8sRepo namespace.INamespaceRepo,
	hrqK8sRepo quota.IHRQRepo, mqRepo node.IMQRepository,
	watchRepo cache.IWatchRepository, logger log.Logger) IUsecase {

	return &nodeUsecase{
		nodeRepo:  nodeK8sRepo,
		nsRepo:    nsK8sRepo,
		hrqRepo:   hrqK8sRepo,
		mqRepo:    mqRepo,
		watchRepo: watchRepo,
		log:       log.NewHelper(logger),
	}
}

func (uc *nodeUsecase) Init() error {

	var err error
	uc.log.Infof("init node usecase")
	defer func() {
		if err != nil {
			uc.log.Errorf("init node usecase failed: %v", err)
		}
	}()

	ctx := context.TODO()
	// subscribe node and pool
	err = uc.mqRepo.Subscribe(ctx, ConsumerName,
		tecons.NatsSubjectNodeBindResourcePool, uc.poolBindHandler)
	if err != nil {
		uc.log.Errorf("subscribe node bind pool failed: %v", err)
		return err
	}
	err = uc.mqRepo.Subscribe(ctx, ConsumerName,
		tecons.NatsSubjectNodeUnbindResourcePool, uc.poolUnBindHandler)
	if err != nil {
		uc.log.Errorf("subscribe node unbind pool failed: %v", err)
		return err
	}

	// subscribe node and tenant
	err = uc.mqRepo.Subscribe(ctx, ConsumerName,
		tecons.NatsSubjectNodeBindTenant, uc.tenantBindHandler)
	if err != nil {
		uc.log.Errorf("subscribe node bind tenant failed: %v", err)
		return err
	}
	err = uc.mqRepo.Subscribe(ctx, ConsumerName,
		tecons.NatsSubjectNodeUnbindTenant, uc.tenantUnBindHandler)
	if err != nil {
		uc.log.Errorf("subscribe node unbind tenant failed: %v", err)
		return err
	}

	uc.log.Infof("init node usecase success.")
	return nil
}

func (uc *nodeUsecase) AllocateNodeToTenant(
	ctx context.Context, node *models.Node, tenant *models.Tenant) error {

	var err error
	uc.log.WithContext(ctx).Infof("allocate node %s to tenant %s(%s)",
		node.Name, tenant.Name, tenant.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"allocate node %s to tenant %s(%s) failed: %v",
				node.Name, tenant.Name, tenant.Id, err)
		}
	}()

	// check node exist in the dedicated pool
	node, err = uc.nodeRepo.Get(ctx, node)
	if err != nil {
		return err
	}
	if node == nil {
		err = errV1.ErrorNodeNotFound("node %s not found.", node.Name)
		return err
	}

	pool := models.ParsePoolTypeFromMap(node.Label)
	if pool != models.ResourcePoolTypeDedicated {
		err = errV1.ErrorQuotaNodeNonExclusive(
			"node %s not in dedicated pool.", node.Name)
		return err
	}

	// allocate
	lables := map[string]string{
		models.NodeDedicatedTenantLabelKey: tenant.Id,
	}
	if err = uc.nodeRepo.Label(ctx, node, lables); err != nil {
		return err
	}

	// update quota of the tenant.
	// tenantNs := models.GetK8sNamespace(
	// 	models.ResourcePoolTypeDedicated, models.TenantSegsInK8sNs, tenant.Id)
	// hrq := &hnc.HierarchicalResourceQuota{
	// 	ObjectMeta: metav1.ObjectMeta{
	// 		Name:      models.ResourceHierarchicalQuotaName,
	// 		Namespace: tenantNs,
	// 	},
	// }
	// k8sHrq, err := uc.hrqRepo.Get(ctx, hrq)
	// if err != nil {
	// 	return err
	// }
	// node.AddResourceToHrq(k8sHrq)
	// if _, err = uc.hrqRepo.Update(ctx, k8sHrq); err != nil {
	// 	return err
	// }

	uc.log.WithContext(ctx).Infof(
		"allocate node %s to tenant %s(%s) success.",
		node.Name, tenant.Name, tenant.Id)
	return nil
}

func (uc *nodeUsecase) DeallocateNodeToTenant(
	ctx context.Context, node *models.Node, tenant *models.Tenant) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"deallocate node %s from tenant %s(%s)",
		node.Name, tenant.Name, tenant.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"deallocate node %s from tenant %s(%s) failed: %v",
				node.Name, tenant.Name, tenant.Id, err)
		}
	}()

	// check node exist in the dedicated pool and the belong to the tenant.
	originNode, err := uc.nodeRepo.Get(ctx, node)
	if err != nil {
		return err
	}
	if originNode == nil {
		err = errV1.ErrorNodeNotFound("node %s not found.", node.Name)
		return err
	}
	node = originNode

	pool := models.ParsePoolTypeFromMap(node.Label)
	if pool != models.ResourcePoolTypeDedicated {
		err = errV1.ErrorQuotaNodeNonExclusive(
			"node %s not in dedicated pool.", node.Name)
		return err
	}
	tenantLabel, ok := node.Label[models.NodeDedicatedTenantLabelKey]

	if !ok || tenantLabel != tenant.Id {
		return errV1.ErrorQuotaNodeDoesNotBelongToTenant(
			"node %s not belong to tenant %s.", node.Name, tenant.Id)
	}

	// unlabel the node.
	keys := []string{models.NodeDedicatedTenantLabelKey}
	if err = uc.nodeRepo.UnLabel(ctx, node, keys); err != nil {
		return err
	}

	// update quota of the tenant.
	// tenantNs := models.GetK8sNamespace(
	// 	models.ResourcePoolTypeDedicated, models.TenantSegsInK8sNs, tenant.Id)
	// hrq := &hnc.HierarchicalResourceQuota{
	// 	ObjectMeta: metav1.ObjectMeta{
	// 		Name:      models.ResourceHierarchicalQuotaName,
	// 		Namespace: tenantNs,
	// 	},
	// }
	// k8sHrq, err := uc.hrqRepo.Get(ctx, hrq)
	// if err != nil {
	// 	return err
	// }
	// node.SubStractResourceToHrq(k8sHrq)
	// if _, err = uc.hrqRepo.Update(ctx, k8sHrq); err != nil {
	// 	return err
	// }

	uc.log.WithContext(ctx).Infof(
		"deallocate node %s from tenant %s(%s) success.",
		node.Name, tenant.Name, tenant.Id)
	return nil

}

func (uc *nodeUsecase) AllocateNodeToWorkspace(
	ctx context.Context, node *models.Node, workspace *models.Workspace) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"allocate node %s to workspace %s(%s)",
		node.Name, workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"allocate node %s to workspace %s(%s) failed: %v",
				node.Name, workspace.Name, workspace.Id, err)
		}
	}()

	// check node exist in the dedicated pool
	node, err = uc.nodeRepo.Get(ctx, node)
	if err != nil {
		return err
	}
	if node == nil {
		err = errV1.ErrorNodeNotFound("node %s not found.", node.Name)
		return err
	}

	pool := models.ParsePoolTypeFromMap(node.Label)
	if pool != models.ResourcePoolTypeDedicated {
		err = errV1.ErrorQuotaNodeNonExclusive(
			"node %s not in dedicated pool.", node.Name)
		return err
	}

	// allocate
	workspaceNs := models.GetK8sNamespace(
		models.ResourcePoolTypeDedicated, models.WorkspaceSegsInK8sNs, workspace.Id)
	lables := map[string]string{
		models.NodeDedicatedWorkspaceLabelKey: workspaceNs,
	}
	if err = uc.nodeRepo.Label(ctx, node, lables); err != nil {
		return err
	}

	// update quota of the workspace.
	hrq := &hnc.HierarchicalResourceQuota{
		ObjectMeta: metav1.ObjectMeta{
			Name:      models.ResourceHierarchicalQuotaName,
			Namespace: workspaceNs,
		},
	}
	k8sHrq, err := uc.hrqRepo.Get(ctx, hrq)
	if err != nil {
		return err
	}
	node.AddResourceToHrq(k8sHrq)
	if _, err = uc.hrqRepo.Update(ctx, k8sHrq); err != nil {
		return err
	}

	uc.log.WithContext(ctx).Infof(
		"allocate node %s to workspace %s(%s) success.",
		node.Name, workspace.Name, workspace.Id)
	return nil
}

func (uc *nodeUsecase) DeallocateNodeToWorkspace(
	ctx context.Context, node *models.Node, workspace *models.Workspace) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"deallocate node %s from workspace %s(%s)",
		node.Name, workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"deallocate node %s from workspace %s(%s) failed: %v",
				node.Name, workspace.Name, workspace.Id, err)
		}
	}()

	// unlabel the node.
	keys := []string{models.NodeDedicatedWorkspaceLabelKey}
	if err = uc.nodeRepo.UnLabel(ctx, node, keys); err != nil {
		return err
	}

	// update quota of the workspace.
	workspaceNs := models.GetK8sNamespace(
		models.ResourcePoolTypeDedicated, models.TenantSegsInK8sNs, workspace.Id)
	hrq := &hnc.HierarchicalResourceQuota{
		ObjectMeta: metav1.ObjectMeta{
			Name:      models.ResourceHierarchicalQuotaName,
			Namespace: workspaceNs,
		},
	}
	k8sHrq, err := uc.hrqRepo.Get(ctx, hrq)
	if err != nil {
		return err
	}
	node.SubStractResourceToHrq(k8sHrq)
	if _, err = uc.hrqRepo.Update(ctx, k8sHrq); err != nil {
		return err
	}

	uc.log.WithContext(ctx).Infof(
		"deallocate node %s from workspace %s(%s) success.",
		node.Name, workspace.Name, workspace.Id)
	return nil
}

func (uc *nodeUsecase) AllocateNodeToResourcePool(
	ctx context.Context, node *models.Node,
	poolType models.ResourcePoolType, poolId string) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"allocate node %s to resource pool %s", node.Name, poolType.String())
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"allocate node %s to resource pool %s failed: %v",
				node.Name, poolType.String(), err)
		}
	}()

	// update node label.
	lables := map[string]string{
		models.ResourcePoolLabelKey:   poolType.String(),
		models.ResourcePoolIdLabelKey: poolId,
	}
	if err = uc.nodeRepo.Label(ctx, node, lables); err != nil {
		return err
	}

	uc.log.WithContext(ctx).Infof(
		"allocate node %s to resource pool %s success.", node.Name, poolType.String())
	return nil
}

func (uc *nodeUsecase) DeallocateNodeToResourcePool(
	ctx context.Context, node *models.Node,
	poolType models.ResourcePoolType, poolId string) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"deallocate node %s from resource pool %s", node.Name, poolType.String())
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"deallocate node %s from resource pool %s failed: %v",
				node.Name, poolType.String(), err)
		}
	}()

	// unlabel the node.
	keys := []string{models.ResourcePoolLabelKey, models.ResourcePoolIdLabelKey}
	if err = uc.nodeRepo.UnLabel(ctx, node, keys); err != nil {
		return err
	}

	uc.log.WithContext(ctx).Infof(
		"deallocate node %s from resource pool %s success.", node.Name, poolType.String())
	return nil
}

func (uc *nodeUsecase) poolBindHandler(msg jetstream.Msg) {
	ctx := context.TODO()
	// pooltype is only bind to pool, node has no pooltype attribute.
	nodeMsg := tecmq.NodeMsgResourcePool{}
	if err := json.Unmarshal(msg.Data(), &nodeMsg); err != nil {
		uc.log.Errorf("unmarshal msg %s to NodeMsgResourcePool failed: %v",
			string(msg.Data()), err)
		if err = msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	ni := uc.watchRepo.GetNodeByUID(types.UID(nodeMsg.NodeUUID))
	if ni == nil {
		uc.log.Errorf("node %s not exist when AllocateNodeToResourcePool.",
			nodeMsg.NodeUUID)
		if err := msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	node := models.NewNodeFromK8sNode(ni.Node)
	poolType := models.ParsePoolTypeFromProductForm(
		billingPb.ProductForm(nodeMsg.ProductForm))
	if poolType == models.ResourcePoolTypeUnknown {
		uc.log.Errorf("unknown resource pool type %d when AllocateNodeToResourcePool.",
			nodeMsg.ProductForm)
		if err := msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	err := uc.AllocateNodeToResourcePool(ctx, node, poolType, nodeMsg.PoolUUID)
	if err != nil {
		uc.log.Errorf("allocate node %s to resource pool %s failed: %v",
			node.Name, poolType.String(), err)
		if err = msg.NakWithDelay(models.MQMessageRedeliverSeconds); err != nil {
			uc.log.Errorf("NakWithDelay msg failed: %v")
		} else {
			uc.log.Infof("NakWithDelay msg success")
		}
		return
	}
	if err = msg.DoubleAck(ctx); err != nil {
		uc.log.Errorf("double ack msg failed: %v", err)
	} else {
		uc.log.Infof("double ack msg success")
	}
}

func (uc *nodeUsecase) poolUnBindHandler(msg jetstream.Msg) {
	nodeMsg := tecmq.NodeMsgResourcePool{}
	if err := json.Unmarshal(msg.Data(), &nodeMsg); err != nil {
		uc.log.Errorf("unmarshal msg %s to NodeMsgResourcePool failed: %v",
			string(msg.Data()), err)
		if err = msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	ni := uc.watchRepo.GetNodeByUID(types.UID(nodeMsg.NodeUUID))
	if ni == nil {
		uc.log.Errorf("node %s not exist when DeallocateNodeToResourcePool.",
			nodeMsg.NodeUUID)
		if err := msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	node := models.NewNodeFromK8sNode(ni.Node)
	poolType := models.ParsePoolTypeFromProductForm(
		billingPb.ProductForm(nodeMsg.ProductForm))
	if poolType == models.ResourcePoolTypeUnknown {
		uc.log.Errorf(
			"unknown resource pool type %d when DeallocateNodeToResourcePool.",
			nodeMsg.ProductForm)
		if err := msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	err := uc.DeallocateNodeToResourcePool(context.TODO(), node, poolType, nodeMsg.PoolUUID)
	if err != nil {
		uc.log.Errorf("deallocate node %s to resource pool %s failed: %v",
			node.Name, poolType.String(), err)
		if err = msg.NakWithDelay(models.MQMessageRedeliverSeconds); err != nil {
			uc.log.Errorf("NakWithDelay msg failed: %v")
		} else {
			uc.log.Infof("NakWithDelay msg success")
		}
		return
	}
	if err = msg.DoubleAck(context.TODO()); err != nil {
		uc.log.Errorf("double ack msg failed: %v", err)
	} else {
		uc.log.Infof("double ack msg success")
	}
}

func (uc *nodeUsecase) tenantBindHandler(msg jetstream.Msg) {
	nodeMsg := tecmq.NodeMsgTenant{}
	if err := json.Unmarshal(msg.Data(), &nodeMsg); err != nil {
		uc.log.Errorf("unmarshal msg %s to NodeMsgTenant failed: %v",
			string(msg.Data()), err)
		if err = msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	ni := uc.watchRepo.GetNodeByUID(types.UID(nodeMsg.NodeUUID))
	if ni == nil {
		uc.log.Errorf("node %s not exist when AllocateNodeToTenant.",
			nodeMsg.NodeUUID)
		if err := msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	node := models.NewNodeFromK8sNode(ni.Node)
	tenant := &models.Tenant{Id: strconv.FormatUint(nodeMsg.TenantID, 10)}
	err := uc.AllocateNodeToTenant(context.TODO(), node, tenant)
	if err != nil {
		uc.log.Errorf("allocate node %s to tenant %s failed: %v",
			node.Name, tenant.Id, err)
		if err = msg.NakWithDelay(models.MQMessageRedeliverSeconds); err != nil {
			uc.log.Errorf("NakWithDelay msg failed: %v")
		} else {
			uc.log.Infof("NakWithDelay msg success")
		}
		return
	}
	if err = msg.DoubleAck(context.TODO()); err != nil {
		uc.log.Errorf("double ack msg failed: %v", err)
	} else {
		uc.log.Infof("double ack msg success")
	}
}

func (uc *nodeUsecase) tenantUnBindHandler(msg jetstream.Msg) {
	nodeMsg := tecmq.NodeMsgTenant{}
	if err := json.Unmarshal(msg.Data(), &nodeMsg); err != nil {
		uc.log.Errorf("unmarshal msg %s to NodeMsgTenant failed: %v",
			string(msg.Data()), err)
		if err = msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	ni := uc.watchRepo.GetNodeByUID(types.UID(nodeMsg.NodeUUID))
	if ni == nil {
		uc.log.Errorf("node %s not exist when DeallocateNodeToTenant.",
			nodeMsg.NodeUUID)
		if err := msg.Term(); err != nil {
			uc.log.Errorf("term msg failed: %v")
		} else {
			uc.log.Infof("term msg success")
		}
		return
	}

	node := models.NewNodeFromK8sNode(ni.Node)
	tenant := &models.Tenant{Id: strconv.FormatUint(nodeMsg.TenantID, 10)}
	err := uc.DeallocateNodeToTenant(context.TODO(), node, tenant)
	if err != nil {
		uc.log.Errorf("deallocate node %s to tenant %s failed: %v",
			node.Name, tenant.Id, err)
		if err = msg.NakWithDelay(models.MQMessageRedeliverSeconds); err != nil {
			uc.log.Errorf("NakWithDelay msg failed: %v")
		} else {
			uc.log.Infof("NakWithDelay msg success")
		}
		return
	}
	if err = msg.DoubleAck(context.TODO()); err != nil {
		uc.log.Errorf("double ack msg failed: %v", err)
	} else {
		uc.log.Infof("double ack msg success")
	}
}
