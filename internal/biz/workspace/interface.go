package workspace

import (
	"context"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type IUsecase interface {
	CreateWorkspace(context.Context, *models.Workspace) (
		map[models.ResourcePoolType]string, error)
	RemoveWorkspace(context.Context, *models.Workspace) error
	UpdateWorkspace(context.Context, *models.Workspace) error
	SyncWorkspace(context.Context, *models.Workspace) (
		map[models.ResourcePoolType]string, error)
}
