// worksapce is the second layer in the hierarchyical
// exist a single namespace for each workspace in the shared pool
package workspace

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/secret"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type workspaceUsecase struct {
	queueRepo  queue.IRepo
	quotaRepo  quota.IQuotaRepo
	hrqRepo    quota.IHRQRepo
	nsRepo     namespace.INamespaceRepo
	subnsRepo  namespace.ISubNamespaceRepo
	secretRepo secret.ISecretRepo

	harbor *models.Harbor
	log    *log.Helper
}

func NewWorkspaceUsecase(
	queueRepo queue.IRepo,
	quotaRepo quota.IQuotaRepo, hrqRepo quota.IHRQRepo,
	nsRepo namespace.INamespaceRepo, subnsRepo namespace.ISubNamespaceRepo,
	secretRepo secret.ISecretRepo,
	harborConfig *conf.Harbor,
	logger log.Logger) IUsecase {

	harbor := models.NewHarborFromPB(harborConfig)
	return &workspaceUsecase{
		queueRepo:  queueRepo,
		hrqRepo:    hrqRepo,
		quotaRepo:  quotaRepo,
		nsRepo:     nsRepo,
		subnsRepo:  subnsRepo,
		secretRepo: secretRepo,
		harbor:     harbor,
		log:        log.NewHelper(logger),
	}
}

func (uc *workspaceUsecase) CreateWorkspace(
	ctx context.Context, workspace *models.Workspace) (
	map[models.ResourcePoolType]string, error) {

	var err error
	uc.log.WithContext(ctx).Infof(
		"create workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"create workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// create k8s ns
	k8sSubNss := workspace.Tok8sSubNss()
	for _, ns := range k8sSubNss {
		_, err = uc.subnsRepo.Create(ctx, ns)
		if err != nil {
			return nil, err
		}
	}

	// create hrq
	hrqMap := workspace.ToK8sHierarchicalQuotaMap()
	if len(hrqMap) == 0 {
		// create default hrq for the workspace.
		hrqMap[models.ResourcePoolTypeDedicated] = models.NewMaxQuota(
			"", workspace.Id, models.ResourcePoolTypeDedicated).ToK8sHierarchicalQuota()
		hrqMap[models.ResourcePoolTypeShared] = models.NewMaxQuota(
			"", workspace.Id, models.ResourcePoolTypeShared).ToK8sHierarchicalQuota()
	}
	for _, quota := range hrqMap {
		_, err = uc.hrqRepo.Create(ctx, quota)
		if err != nil {
			return nil, err
		}
	}

	// create harbor secret
	for _, ns := range k8sSubNss {
		secret := uc.harbor.ToK8sSecret(ns.Name)
		_, err = uc.secretRepo.Create(ctx, secret)
		if err != nil {
			return nil, err
		}
	}

	res := map[models.ResourcePoolType]string{}
	for pool, ns := range k8sSubNss {
		res[pool] = ns.Name
	}

	uc.log.WithContext(ctx).Infof(
		"create workspace %s(%s) success", workspace.Name, workspace.Id)
	return res, nil
}

func (uc *workspaceUsecase) RemoveWorkspace(
	ctx context.Context, workspace *models.Workspace) error {

	// log it.
	var err error
	uc.log.WithContext(ctx).Infof(
		"remove workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"remove workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// delete quota
	k8sSubNss := workspace.Tok8sSubNss()
	for _, ns := range k8sSubNss {
		hrq := &hnc.HierarchicalResourceQuota{
			ObjectMeta: metav1.ObjectMeta{
				Name:      models.ResourceHierarchicalQuotaName,
				Namespace: ns.Name,
			},
		}
		err = uc.hrqRepo.Delete(ctx, hrq)
		if err != nil {
			return err
		}
	}

	// delete harbor secret
	for _, ns := range k8sSubNss {
		err = uc.secretRepo.Delete(ctx, &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      uc.harbor.SecretName,
				Namespace: ns.Name,
			},
		})
		if err != nil {
			return err
		}
	}

	// delete namespace
	for _, ns := range k8sSubNss {
		err = uc.subnsRepo.Delete(ctx, ns)
		if err != nil {
			return err
		}
	}

	uc.log.WithContext(ctx).Infof(
		"remove workspace %s(%s) success", workspace.Name, workspace.Id)
	return nil
}

func (uc *workspaceUsecase) UpdateWorkspace(
	ctx context.Context, workspace *models.Workspace) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"update workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"update workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// update meta info of workspace
	k8sSubNsMap := workspace.Tok8sSubNss()
	for _, ns := range k8sSubNsMap {
		originK8s, e := uc.subnsRepo.Get(ctx, ns)
		if e != nil {
			err = e
			return err
		}
		if originK8s == nil {
			err = errV1.ErrorKubernetesOperatorError(
				"subnamespace %s not found.", ns.Name)
			return err
		}

		err = models.MergeK8sSubNs(originK8s, ns)
		if err != nil {
			return err
		}

		_, err = uc.subnsRepo.Update(ctx, originK8s)
		if err != nil {
			return err
		}
	}

	// update quota related.
	hrqMap := workspace.ToK8sHierarchicalQuotaMap()
	for _, quota := range hrqMap {
		k8sQuota, e := uc.hrqRepo.Get(ctx, quota)
		if e != nil {
			err = e
			return err
		}
		if k8sQuota == nil {
			_, err = uc.hrqRepo.Create(ctx, quota)
			if err != nil {
				return err
			}
		} else {
			k8sQuota.Spec = quota.Spec
			_, err = uc.hrqRepo.Update(ctx, k8sQuota)
			if err != nil {
				return err
			}
		}
	}

	uc.log.WithContext(ctx).Infof(
		"update workspace %s(%s) success", workspace.Name, workspace.Id)
	return nil
}

func (uc *workspaceUsecase) SyncWorkspace(
	ctx context.Context, workspace *models.Workspace) (
	map[models.ResourcePoolType]string, error) {

	var err error
	uc.log.WithContext(ctx).Infof(
		"sync workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"sync workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// update meta info of workspace
	k8sSubNsMap := workspace.Tok8sSubNss()
	for _, ns := range k8sSubNsMap {
		originK8s, e := uc.subnsRepo.Get(ctx, ns)
		if err = e; err != nil {
			return nil, err
		}
		if originK8s == nil {
			_, err = uc.subnsRepo.Create(ctx, ns)
			if err != nil {
				return nil, err
			}
			continue
		}

		err = models.MergeK8sSubNs(originK8s, ns)
		if err != nil {
			return nil, err
		}
		_, err = uc.subnsRepo.Update(ctx, originK8s)
		if err != nil {
			return nil, err
		}
	}

	// sync hrq
	hrqMap := workspace.ToK8sHierarchicalQuotaMap()
	for i := 1; i < int(models.ResourcePoolTypeCnt); i++ {
		poolType := models.ResourcePoolType(i)
		if poolType == models.ResourcePoolTypeReserved {
			continue
		}

		if _, ok := hrqMap[poolType]; ok {
			continue
		}

		// reset other quotas
		resetQuota := models.NewMaxQuota(
			"", workspace.Id, poolType).ToK8sHierarchicalQuota()
		hrqMap[poolType] = resetQuota
	}

	for _, hrq := range hrqMap {
		k8sQuota, e := uc.hrqRepo.Get(ctx, hrq)
		if err = e; err != nil {
			return nil, err
		}
		if k8sQuota == nil {
			_, err = uc.hrqRepo.Create(ctx, hrq)
			if err != nil {
				return nil, err
			}
			continue
		}
		newQuota := k8sQuota.DeepCopy()
		newQuota.Spec = hrq.Spec
		_, err = uc.hrqRepo.Patch(ctx, k8sQuota, newQuota)
		if err != nil {
			return nil, err
		}
	}

	// sync harbor secret
	for _, ns := range k8sSubNsMap {
		secret := uc.harbor.ToK8sSecret(ns.Name)
		_, err = uc.secretRepo.Apply(ctx, secret)
		if err != nil {
			return nil, err
		}
	}

	res := map[models.ResourcePoolType]string{}
	for pool, ns := range k8sSubNsMap {
		res[pool] = ns.Name
	}

	uc.log.WithContext(ctx).Infof(
		"sync workspace %s(%s) success", workspace.Name, workspace.Id)
	return res, nil
}
