// only one layer namespace and quota, no hierarchy.
// shared pool is only one.
package workspace

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/namespace"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/quota"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"
)

type workspaceUsecaseV1 struct {
	queueRepo queue.IRepo
	quotaRepo quota.IQuotaRepo
	nsRepo    namespace.INamespaceRepo

	log *log.Helper
}

func NewWorkspaceUsecaseV1(
	queueRepo queue.IRepo, quotaRepo quota.IQuotaRepo,
	nsRepo namespace.INamespaceRepo, logger log.Logger) IUsecase {

	return &workspaceUsecase{
		queueRepo: queueRepo,
		quotaRepo: quotaRepo,
		nsRepo:    nsRepo,
		log:       log.NewHelper(logger),
	}
}

func (uc *workspaceUsecaseV1) CreateWorkspace(
	ctx context.Context, workspace *models.Workspace) (
	map[models.ResourcePoolType]string, error) {

	var err error
	uc.log.WithContext(ctx).Infof(
		"create workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"create workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// create k8s ns
	k8sNsMap := workspace.ToK8sNamespaces()
	for pool, ns := range k8sNsMap {
		if pool == models.ResourcePoolTypeShared {
			continue
		}

		_, err = uc.nsRepo.Create(ctx, ns)
		if err != nil {
			return nil, err
		}
	}

	// create quota
	quotas := workspace.ToK8sQuotas()
	for _, quota := range quotas {
		_, err = uc.quotaRepo.Create(ctx, quota)
		if err != nil {
			return nil, err
		}
	}

	res := map[models.ResourcePoolType]string{}
	for pool, ns := range k8sNsMap {
		res[pool] = ns.Name
	}

	uc.log.WithContext(ctx).Infof(
		"create workspace %s(%s) success", workspace.Name, workspace.Id)
	return res, nil
}

func (uc *workspaceUsecaseV1) RemoveWorkspace(
	ctx context.Context, workspace *models.Workspace) error {

	// log it.
	var err error
	uc.log.WithContext(ctx).Infof(
		"remove workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"remove workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// delete quota
	k8sNsMap := workspace.ToK8sNamespaces()
	for pool, ns := range k8sNsMap {
		if pool == models.ResourcePoolTypeShared {
			continue
		}

		quota := &corev1.ResourceQuota{
			ObjectMeta: metav1.ObjectMeta{
				Name:      models.ResourceQuotaName,
				Namespace: ns.Name,
			},
		}
		err = uc.quotaRepo.Delete(ctx, quota)
		if err != nil {
			return err
		}
	}

	// delte namespace
	for pool, ns := range k8sNsMap {
		if pool == models.ResourcePoolTypeShared {
			continue
		}

		err = uc.nsRepo.Delete(ctx, ns)
		if err != nil {
			return err
		}
	}

	uc.log.WithContext(ctx).Infof(
		"remove workspace %s(%s) success", workspace.Name, workspace.Id)
	return nil
}

func (uc *workspaceUsecaseV1) UpdateWorkspace(
	ctx context.Context, workspace *models.Workspace) error {

	var err error
	uc.log.WithContext(ctx).Infof(
		"update workspace %s(%s)", workspace.Name, workspace.Id)
	defer func() {
		if err != nil {
			uc.log.WithContext(ctx).Errorf(
				"update workspace %s(%s) failed: %v",
				workspace.Name, workspace.Id, err)
		}
	}()

	// update meta info of workspace
	k8sNsMap := workspace.ToK8sNamespaces()
	for pool, ns := range k8sNsMap {
		if pool == models.ResourcePoolTypeShared {
			continue
		}

		originK8s, e := uc.nsRepo.Get(ctx, ns)
		if e != nil {
			err = e
			return err
		}
		if originK8s == nil {
			err = errV1.ErrorKubernetesOperatorError(
				"namespace %s not found.", ns.Name)
			return err
		}

		err = models.MergeK8sNamespace(originK8s, ns)
		if err != nil {
			return err
		}

		_, err = uc.nsRepo.Update(ctx, originK8s)
		if err != nil {
			return err
		}
	}

	// update quota related.
	quotas := workspace.ToK8sQuotas()
	for _, quota := range quotas {
		k8sQuota, e := uc.quotaRepo.Get(ctx, quota)
		if e != nil {
			err = e
			return err
		}
		if k8sQuota == nil {
			_, err = uc.quotaRepo.Create(ctx, quota)
			if err != nil {
				return err
			}
		} else {
			k8sQuota.Spec = quota.Spec
			_, err = uc.quotaRepo.Update(ctx, k8sQuota)
			if err != nil {
				return err
			}
		}
	}

	uc.log.WithContext(ctx).Infof(
		"update workspace %s(%s) success", workspace.Name, workspace.Id)
	return nil
}

func (uc *workspaceUsecaseV1) SyncWorkspace(
	context.Context, *models.Workspace) (
	map[models.ResourcePoolType]string, error) {

	return nil, fmt.Errorf("Unimplemented")
}
