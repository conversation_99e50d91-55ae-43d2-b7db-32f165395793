package biz

import (
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/google/wire"

	v1 "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/node"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/pool"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/tenant"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/watch"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/biz/workspace"
)

var (
	// ErrUserNotFound is user not found.
	ErrUserNotFound = errors.NotFound(
		v1.ErrorReason_USER_NOT_FOUND.String(), "user not found")
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(
	tenant.NewTenantUsecase, workspace.NewWorkspaceUsecase,
	node.NewNodeUsecase, queue.NewQueueUsecase, watch.NewWatchUsecase,
	pool.NewUsecase)
