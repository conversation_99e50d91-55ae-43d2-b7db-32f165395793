package queue

import (
	"context"

	repo "git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/repository/queue"
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"github.com/go-kratos/kratos/v2/log"
)

type queueUsecase struct {
	queueRepo repo.IRepo
	log       *log.Helper
}

func NewQueueUsecase(queue repo.IRepo, logger log.Logger) IUsecase {

	return &queueUsecase{
		queueRepo: queue,
		log:       log.NewHelper(logger),
	}
}

func (uc *queueUsecase) Init() error {

	var err error
	uc.log.Infof("init queue usecase")
	defer func() {
		if err != nil {
			uc.log.Errorf("init queue usecase error: %v", err)
		}
	}()

	// init three queue, created if not exist.
	for i := 1; i < int(models.ResourcePoolTypeCnt); i++ {
		q := models.NewNamedQueue(models.ResourcePoolType(i).String())
		originQ, e := uc.queueRepo.Get(context.TODO(), q)
		if e != nil {
			err = e
			return err
		}
		if originQ != nil {
			uc.log.Infof("queue %s already exist", q.Name)
			continue
		}
		// create it
		err = uc.queueRepo.Create(context.TODO(), q)
		if err != nil {
			return err
		}
		uc.log.Infof("init queue %s success", q.Name)
	}

	uc.log.Infof("init queue usecase success")
	return nil
}
