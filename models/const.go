package models

import "time"

const (
	ResourcePoolK8sNsPrefix        = "kubecube"
	ResourcePoolLabelKey           = "tecoai.com/resource-pool"
	ResourcePoolIdLabelKey         = "tecoai.com/resource-pool-id"
	ResourcePoolTypeSharedK8sNs    = "kubecube-pool-3"
	ResourcePoolTypeReservedK8sNs  = "kubecube-pool-2"
	ResourcePoolTypeDedicatedK8sNs = "kubecube-pool-1"

	TenantSegsInK8sNs    = "tenant"
	WorkspaceSegsInK8sNs = "workspace"
	ProjectSegsInK8sNs   = "project"

	ResourceLimitsPrefix          = "limits."
	ResourceRequestsPrefix        = "requests."
	ResourceQuotaName             = "tecoai-quota"
	HncResourceQuotaName          = "hrq.hnc.x-k8s.io"
	ResourceHierarchicalQuotaName = "pool.3"

	NodeTypeManagerLabelKey        = "tecoai.com/is-manager"
	NodeTypeComputeLabelKey        = "tecoai.com/is-compute"
	NodeDedicatedTenantLabelKey    = "tecoai.com/dedicated-tenant"
	NodeDedicatedWorkspaceLabelKey = "tecoai.com/dedicated-workspace"
	NodeCardModelLabelKey          = "tecoai.com/card-model"

	PodTenantLabelKey       = "tenant"
	PodWorkspaceLabelKey    = "workspace"
	PodResourceModeLabelKey = "resourceMode"

	NSTenantNameAnnotation    = "tecoai.com/tenant-name"
	NSWorkspaceNameAnnotation = "tecoai.com/workspace-name"

	MQMessageRedeliverSeconds = 15 * time.Second
	NodeInfoRetrySeconds      = 15 * time.Second
	InitialPoolUsageDelay     = 60 * time.Second
)

// hnc related const
const (
	// HncInherited means resource is inherited form upon namespace by hnc
	HncInherited = "hnc.x-k8s.io/inherited-from"

	HncPoolLabel = "kubecube.hnc.x-k8s.io/pool"

	HncTenantLabel = "kubecube.hnc.x-k8s.io/tenant"

	HncWorkspaceLabel = "kubecube.hnc.x-k8s.io/workspace"

	HncProjectLabel = "kubecube.hnc.x-k8s.io/project"

	HncIncludedNsLabel = "hnc.x-k8s.io/included-namespace"

	/*
		Namespace depth is relative to current namespace depth.
		Example:
		tenant-1
		└── [s] workspace-1
			   └── [s] project-1
		ns-1 namespace has three depth label:
		1. ns-1.tree.hnc.x-k8s.io/depth: "0"
		2. workspace-1.tree.hnc.x-k8s.io/depth: "1"
		3. project-1.tree.hnc.x-k8s.io/depth: "2"
	*/
	HncProjectDepth   = "0"
	HncWorkspaceDepth = "1"
	HncTenantDepth    = "2"

	// HncSuffix record depth of namespace in HNC
	HncSuffix = ".tree.hnc.x-k8s.io/depth"

	// HncAnnotation must exist in sub namespace
	HncAnnotation = "hnc.x-k8s.io/subnamespace-of"
)
