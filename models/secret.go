package models

import (
	"fmt"

	"git-plat.tecorigin.net/ai-platform/scheduling-manager/internal/conf"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Harbor struct {
	Url              string
	Username         string
	Password         string
	InsecureRegistry bool
	SecretName       string
}

func NewHarborFromPB(conf *conf.Harbor) *Harbor {
	return &Harbor{
		Url:              conf.Url,
		Username:         conf.Username,
		Password:         conf.Password,
		InsecureRegistry: conf.InsecureRegistry,
		SecretName:       conf.SecretName,
	}
}

func (h *Harbor) ToK8sSecret(ns string) *corev1.Secret {

	dockerConfigJson := h.GetDockerConfigJson()

	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      h.SecretName,
			Namespace: ns,
		},
		Type: corev1.SecretTypeDockerConfigJson,
		Data: map[string][]byte{
			corev1.DockerConfigJsonKey: []byte(dockerConfigJson),
		},
	}
}

func (h *Harbor) GetDockerConfigJson() string {
	return fmt.Sprintf(`{"auths":{"%s":{"password":"%s","username":"%s"}}}`,
		h.Url,
		h.Password,
		h.Username,
	)
}
