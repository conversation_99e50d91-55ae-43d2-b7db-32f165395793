package models

import (
	"fmt"
	"regexp"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/sets"

	v1 "git-plat.tecorigin.net/ai-platform/console-backend/api/enum/v1"
)

// namespace pattern
var SpecERNamePattern = regexp.MustCompile(`^(teco|nvidia|hygon)\((.+)\)$`)

type ExtendedResourceType int32

const (
	ExtendedResourceUnknown ExtendedResourceType = iota
	ExtendedResourceGpu
	ExtendedResourceTeco
	ExtendedResourceTecocore
	ExtendedResourceDcu

	ExtendedResourceCnt
)

func String2ExtendedResourceType(value string) ExtendedResourceType {
	iValue := strings.ToLower(value)
	switch iValue {
	case "gpu":
		return ExtendedResourceGpu
	case "teco":
		return ExtendedResourceTeco
	case "tecocore":
		return ExtendedResourceTecocore
	case "dcu":
		return ExtendedResourceDcu
	}
	return ExtendedResourceUnknown
}

func K8sResourceName2ExtendedResourceType(value corev1.ResourceName) ExtendedResourceType {
	iValue := strings.ToLower(string(value))
	switch iValue {
	case "nvidia.com/gpu":
		fallthrough
	case ResourceRequestsPrefix + "nvidia.com/gpu":
		fallthrough
	case ResourceLimitsPrefix + "nvidia.com/gpu":
		return ExtendedResourceGpu

	case "teco.com/gpu":
		fallthrough
	case ResourceRequestsPrefix + "teco.com/gpu":
		fallthrough
	case ResourceLimitsPrefix + "teco.com/gpu":
		return ExtendedResourceTeco

	case "teco.com/core":
		fallthrough
	case ResourceRequestsPrefix + "teco.com/core":
		fallthrough
	case ResourceLimitsPrefix + "teco.com/core":
		return ExtendedResourceTecocore

	case "hygon.com/dcu":
		fallthrough
	case ResourceRequestsPrefix + "hygon.com/dcu":
		fallthrough
	case ResourceLimitsPrefix + "hygon.com/dcu":
		return ExtendedResourceDcu
	default:
		return ExtendedResourceUnknown
	}
}

func (typeV ExtendedResourceType) String() string {
	switch typeV {
	case ExtendedResourceUnknown:
		return "unknown"
	case ExtendedResourceGpu:
		return "gpu"
	case ExtendedResourceTeco:
		return "teco"
	case ExtendedResourceTecocore:
		return "tecocore"
	case ExtendedResourceDcu:
		return "dcu"
	}
	return fmt.Sprintf("ExtendedRresourceType(%d)", typeV)
}

func (typeV ExtendedResourceType) K8sResourceName() string {
	switch typeV {
	case ExtendedResourceUnknown:
		return "unknown"
	case ExtendedResourceGpu:
		return "nvidia.com/gpu"
	case ExtendedResourceTeco:
		return "teco.com/gpu"
	case ExtendedResourceTecocore:
		return "teco.com/core"
	case ExtendedResourceDcu:
		return "hygon.com/dcu"
	default:
		return fmt.Sprintf("ExtendedRresourceType(%d)", typeV)
	}
}

func Vendor2ExtendedResourceType(vendor string) ExtendedResourceType {
	vendor = strings.ToLower(vendor)
	switch vendor {
	case "nvidia":
		return ExtendedResourceGpu
	case "teco":
		return ExtendedResourceTeco
	case "tecocore":
		return ExtendedResourceTecocore
	case "hygon":
		return ExtendedResourceDcu
	}
	return ExtendedResourceUnknown
}

func (typeV ExtendedResourceType) Vendor() string {
	switch typeV {
	case ExtendedResourceUnknown:
		return "unknown"
	case ExtendedResourceGpu:
		return "nvidia"
	case ExtendedResourceTeco:
		return "teco"
	case ExtendedResourceTecocore:
		return "teco"
	case ExtendedResourceDcu:
		return "hygon"
	default:
		return fmt.Sprintf("ExtendedRresourceType(%d)", typeV)
	}
}

func (typeV ExtendedResourceType) ToUserQuotaType() v1.QuotaType {
	switch typeV {
	case ExtendedResourceGpu:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA
	case ExtendedResourceTeco:
		return v1.QuotaType_QUOTA_TYPE_GPU_TECO
	case ExtendedResourceTecocore:
		return v1.QuotaType_QUOTA_TYPE_UNSPECIFIED
	case ExtendedResourceDcu:
		return v1.QuotaType_QUOTA_TYPE_UNSPECIFIED
	}
	return v1.QuotaType_QUOTA_TYPE_UNSPECIFIED
}

var KnownER = sets.New(
	"teco.com/gpu", "teco.com/core", "nvidia.com/gpu", "hygon.com/dcu")
