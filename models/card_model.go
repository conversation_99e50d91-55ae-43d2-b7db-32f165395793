package models

import v1 "git-plat.tecorigin.net/ai-platform/console-backend/api/enum/v1"

type CardModel int32

const (
	// todo: represent unknown card model or teccore sometimes
	CardModel_Unknown CardModel = iota

	CardModel_NVIDIA_RTX3060
	CardModel_NVIDIA_RTX3070
	CardModel_NVIDIA_RTX3080
	CardModel_NVIDIA_RTX3090
	CardModel_NVIDIA_RTX4060
	CardModel_NVIDIA_RTX4070
	CardModel_NVIDIA_RTX4080
	CardModel_NVIDIA_RTX4090

	CardModel_NVIDIA_V100
	CardModel_NVIDIA_T40
	CardModel_NVIDIA_A100
	CardModel_NVIDIA_B200
	CardModel_NVIDIA_H100
	CardModel_NVIDIA_H200
	CardModel_NVIDIA_H800

	CardModel_TECO_T100
	CardModel_TECO_T110
	CardModel_TECO_I100
	CardModel_TECO_I110

	CardModel_Cnt
)

func StringToCardModel(labelValue string) CardModel {
	switch labelValue {
	case "nvidia-RTX3060":
		return CardModel_NVIDIA_RTX3060
	case "nvidia-RTX3070":
		return CardModel_NVIDIA_RTX3070
	case "nvidia-RTX3080":
		return CardModel_NVIDIA_RTX3080
	case "nvidia-3090":
		fallthrough
	case "nvidia-RTX3090":
		return CardModel_NVIDIA_RTX3090
	case "nvidia-RTX4060":
		return CardModel_NVIDIA_RTX4060
	case "nvidia-RTX4070":
		return CardModel_NVIDIA_RTX4070
	case "nvidia-RTX4080":
		return CardModel_NVIDIA_RTX4080
	case "nvidia-RTX4090":
		return CardModel_NVIDIA_RTX4090
	case "nvidia-V100":
		return CardModel_NVIDIA_V100
	case "nvidia-T40":
		return CardModel_NVIDIA_T40
	case "nvidia-A100":
		return CardModel_NVIDIA_A100
	case "nvidia-B200":
		return CardModel_NVIDIA_B200
	case "nvidia-H100":
		return CardModel_NVIDIA_H100
	case "nvidia-H200":
		return CardModel_NVIDIA_H200
	case "nvidia-H800":
		return CardModel_NVIDIA_H800
	case "teco-T100":
		return CardModel_TECO_T100
	case "teco-T110":
		return CardModel_TECO_T110
	case "teco-I100":
		return CardModel_TECO_I100
	case "teco-I110":
		return CardModel_TECO_I110
	}
	return CardModel_Unknown
}

func ParseCardModelFromMap(labels map[string]string) CardModel {
	if labels == nil {
		return CardModel_Unknown
	}
	return StringToCardModel(labels[NodeCardModelLabelKey])
}

func (model CardModel) ToQuotaType() v1.QuotaType {
	switch model {
	case CardModel_NVIDIA_RTX3060:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_3060
	case CardModel_NVIDIA_RTX3070:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_3070
	case CardModel_NVIDIA_RTX3080:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_3080
	case CardModel_NVIDIA_RTX3090:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_3090
	case CardModel_NVIDIA_RTX4060:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_4060
	case CardModel_NVIDIA_RTX4070:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_4070
	case CardModel_NVIDIA_RTX4080:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_4080
	case CardModel_NVIDIA_RTX4090:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_4090
	case CardModel_NVIDIA_V100:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_V100
	case CardModel_NVIDIA_T40:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_T40
	case CardModel_NVIDIA_A100:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_A100
	case CardModel_NVIDIA_B200:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_B200
	case CardModel_NVIDIA_H100:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_H100
	case CardModel_NVIDIA_H200:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_H200
	case CardModel_NVIDIA_H800:
		return v1.QuotaType_QUOTA_TYPE_GPU_NVIDIA_H800
	case CardModel_TECO_T100:
		return v1.QuotaType_QUOTA_TYPE_GPU_TECO_T100
	case CardModel_TECO_T110:
		return v1.QuotaType_QUOTA_TYPE_GPU_TECO_T110
	case CardModel_TECO_I100:
		return v1.QuotaType_QUOTA_TYPE_GPU_TECO_I100
	case CardModel_TECO_I110:
		return v1.QuotaType_QUOTA_TYPE_GPU_TECO_I110
	}
	return v1.QuotaType_QUOTA_TYPE_UNSPECIFIED
}

func (model CardModel) ExtendedResourceType() ExtendedResourceType {
	switch model {
	case CardModel_NVIDIA_RTX3060, CardModel_NVIDIA_RTX3070, CardModel_NVIDIA_RTX3080,
		CardModel_NVIDIA_RTX3090, CardModel_NVIDIA_RTX4060, CardModel_NVIDIA_RTX4070,
		CardModel_NVIDIA_RTX4080, CardModel_NVIDIA_RTX4090, CardModel_NVIDIA_V100,
		CardModel_NVIDIA_T40, CardModel_NVIDIA_A100, CardModel_NVIDIA_B200,
		CardModel_NVIDIA_H100, CardModel_NVIDIA_H200, CardModel_NVIDIA_H800:
		return ExtendedResourceGpu
	case CardModel_TECO_T100, CardModel_TECO_T110,
		CardModel_TECO_I100, CardModel_TECO_I110:
		return ExtendedResourceTeco
	}
	return ExtendedResourceUnknown
}

func (model CardModel) String() string {
	switch model {
	case CardModel_NVIDIA_RTX3060:
		return "nvidia-RTX3060"
	case CardModel_NVIDIA_RTX3070:
		return "nvidia-RTX3070"
	case CardModel_NVIDIA_RTX3080:
		return "nvidia-RTX3080"
	case CardModel_NVIDIA_RTX3090:
		return "nvidia-RTX3090"
	case CardModel_NVIDIA_RTX4060:
		return "nvidia-RTX4060"
	case CardModel_NVIDIA_RTX4070:
		return "nvidia-RTX4070"
	case CardModel_NVIDIA_RTX4080:
		return "nvidia-RTX4080"
	case CardModel_NVIDIA_RTX4090:
		return "nvidia-RTX4090"
	case CardModel_NVIDIA_V100:
		return "nvidia-V100"
	case CardModel_NVIDIA_T40:
		return "nvidia-T40"
	case CardModel_NVIDIA_A100:
		return "nvidia-A100"
	case CardModel_NVIDIA_B200:
		return "nvidia-B200"
	case CardModel_NVIDIA_H100:
		return "nvidia-H100"
	case CardModel_NVIDIA_H200:
		return "nvidia-H200"
	case CardModel_NVIDIA_H800:
		return "nvidia-H800"
	case CardModel_TECO_T100:
		return "teco-T100"
	case CardModel_TECO_T110:
		return "teco-T110"
	case CardModel_TECO_I100:
		return "teco-I100"
	case CardModel_TECO_I110:
		return "teco-I110"
	default:
		return "unknown"
	}
}

func (model CardModel) Cores() int {
	switch model {
	case CardModel_TECO_T100:
		return 4
	case CardModel_TECO_T110:
		return 4
	case CardModel_TECO_I100:
		return 3
	case CardModel_TECO_I110:
		return 3
	default:
		return 1
	}
}
