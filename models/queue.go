package models

import (
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

type QueueState int32

const (
	QueueStateUnknown QueueState = iota
	QueueStateOpen
	QueueStateClosed
	QueueStateClosing
)

func String2QueueState(value string) QueueState {
	value = strings.ToLower(value)
	switch value {
	case "open":
		return QueueStateOpen
	case "closed":
		return QueueStateClosed
	case "closing":
		return QueueStateClosing
	default:
		return QueueStateUnknown
	}
}

func (state QueueState) String() string {
	switch state {
	case QueueStateOpen:
		return "Open"
	case QueueStateClosed:
		return "Closed"
	case QueueStateClosing:
		return "Closing"
	default:
		return "Unknown"
	}
}

type Queue struct {
	Name        string
	Weight      int32
	Reclaimable bool
	Capability  *QuotaValue
	State       QueueState
}

func NewNamedQueue(name string) *Queue {
	return &Queue{
		Name:        name,
		Weight:      1,
		Reclaimable: false,
		State:       QueueStateOpen,
	}
}

func NewQueueFromK8sQueue(queueObj *v1beta1.Queue) *Queue {
	res := &Queue{
		Name:        queueObj.Name,
		Weight:      queueObj.Spec.Weight,
		Reclaimable: *queueObj.Spec.Reclaimable,
		Capability:  NewQuotaValueFromK8sResourceList(queueObj.Spec.Capability),
		State:       String2QueueState(string(queueObj.Status.State)),
	}
	return res
}

func (q *Queue) ToK8sQueue() *v1beta1.Queue {
	res := &v1beta1.Queue{
		ObjectMeta: metav1.ObjectMeta{
			Name: q.Name,
		},
		Spec: v1beta1.QueueSpec{
			Weight:      q.Weight,
			Reclaimable: &q.Reclaimable,
			Capability:  q.Capability.ToK8sResourceList(),
		},
		Status: v1beta1.QueueStatus{
			State: v1beta1.QueueState(q.State.String()),
		},
	}
	return res
}
