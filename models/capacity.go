package models

import (
	"errors"
	"strings"
)

// 定义容量单位的字节数
const (
	B    = 1
	Byte = B
	KB   = 1024
	MB   = KB * 1024
	GB   = MB * 1024
	TB   = GB * 1024
	PB   = TB * 1024
	EB   = PB * 1024
)

// ConvertUnit 将容量从一个单位转换为另一个单位
func ConvertUnit(value uint64, fromUnit, toUnit string) (uint64, error) {
	fromUnit = strings.ToUpper(fromUnit)
	toUnit = strings.ToUpper(toUnit)

	// 获取源单位的字节数
	fromBytes, err := getBytes(fromUnit)
	if err != nil {
		return 0, err
	}

	// 获取目标单位的字节数
	toBytes, err := getBytes(toUnit)
	if err != nil {
		return 0, err
	}

	// 进行单位转换
	return value * uint64(fromBytes) / uint64(toBytes), nil
}

// getBytes 根据单位名称返回对应的字节数
func getBytes(unit string) (uint64, error) {
	unit = strings.ToUpper(unit)
	switch unit {
	case "B":
		return B, nil
	case "BYTE":
		return Byte, nil
	case "KB":
		return KB, nil
	case "MB":
		return MB, nil
	case "GB":
		return GB, nil
	case "TB":
		return TB, nil
	case "PB":
		return PB, nil
	case "EB":
		return EB, nil
	default:
		return 0, errors.New("unknown unit")
	}
}
