package models

import (
	pb "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// corresponding to tenant in business, not k8s.
type Tenant struct {
	Id   string `json:"id"`
	Name string `json:"name"`

	// resource pool -> quota
	Quotas map[ResourcePoolType]*Quota `json:"quotas,omitempty"`
}

func NewTenantFromPBCreateRequest(req *pb.CreateTenantRequest) *Tenant {
	res := &Tenant{
		Id:     req.TenantId,
		Name:   req.Name,
		Quotas: make(map[ResourcePoolType]*Quota),
	}

	for poolType, quota := range req.Quotas {
		pool := ResourcePoolType(poolType)

		er := make(map[ExtendedResourceType]uint64, 0)
		for k, v := range quota.ExtendedResource {
			er[ExtendedResourceType(k)] = v
		}

		res.Quotas[pool] = &Quota{
			Name:     ResourceQuotaName,
			TenantId: req.TenantId,
			Type:     pool,
			Value: &QuotaValue{
				Cpu: &Cpu{
					Num:          quota.Cpu.Num,
					Architecture: quota.Cpu.Architecture,
				},
				Memory:           quota.Memory,
				ExtendedResource: er,
			},
		}
	}

	return res
}

func NewTenantFromPBRemoveRequest(req *pb.RemoveTenantRequest) *Tenant {
	return &Tenant{Id: req.TenantId}
}

func (t *Tenant) ToK8sNamespaces() map[ResourcePoolType]*corev1.Namespace {
	namespaces := make(map[ResourcePoolType]*corev1.Namespace)
	for i := 1; i < int(ResourcePoolTypeCnt); i++ {
		pool := ResourcePoolType(i)
		if pool == ResourcePoolTypeReserved {
			continue
		}

		nsName := GetK8sNamespace(pool, TenantSegsInK8sNs, t.Id)
		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: nsName,
				Labels: map[string]string{
					HncTenantLabel: t.Id,
				},
				Annotations: map[string]string{},
			},
		}
		if t.Name != "" {
			ns.Annotations[NSTenantNameAnnotation] = t.Name
		}
		namespaces[ResourcePoolType(i)] = ns
	}
	return namespaces
}

func (t *Tenant) Tok8sSubNss() map[ResourcePoolType]*hnc.SubnamespaceAnchor {
	res := make(map[ResourcePoolType]*hnc.SubnamespaceAnchor)
	for i := 1; i < int(ResourcePoolTypeCnt); i++ {
		pool := ResourcePoolType(i)
		if pool == ResourcePoolTypeReserved {
			continue
		}

		nsName := GetK8sNamespace(pool, TenantSegsInK8sNs, t.Id)
		subns := &hnc.SubnamespaceAnchor{
			ObjectMeta: metav1.ObjectMeta{
				Name:        nsName,
				Namespace:   pool.ToK8sNsName(),
				Labels:      map[string]string{},
				Annotations: map[string]string{},
			},
			Spec: hnc.SubnamespaceAnchorSpec{
				Labels: []hnc.MetaKVP{
					{
						Key:   HncPoolLabel,
						Value: pool.String(),
					},
					{
						Key:   HncTenantLabel,
						Value: t.Id,
					},
				},
			},
		}

		if t.Name != "" {
			subns.Annotations[NSTenantNameAnnotation] = t.Name
		}
		res[pool] = subns
	}
	return res
}

func (t *Tenant) ToK8sHierarchicalQuotas() []*hnc.HierarchicalResourceQuota {
	res := make([]*hnc.HierarchicalResourceQuota, 0)
	for _, iQuota := range t.Quotas {
		quota := iQuota.ToK8sHierarchicalQuota()
		res = append(res, quota)
	}
	return res
}

func (t *Tenant) ToK8sHierarchicalQuotaMap() map[ResourcePoolType]*hnc.HierarchicalResourceQuota {
	hrqMap := map[ResourcePoolType]*hnc.HierarchicalResourceQuota{}
	for poolType, iQuota := range t.Quotas {
		quota := iQuota.ToK8sHierarchicalQuota()
		hrqMap[poolType] = quota
	}
	return hrqMap
}
