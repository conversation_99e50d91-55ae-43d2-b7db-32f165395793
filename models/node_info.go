package models

import (
	"math"

	"volcano.sh/volcano/pkg/scheduler/api"
	"volcano.sh/volcano/pkg/scheduler/api/devices/teco"
)

func IsNodeInfoBizEmpty(ni *api.NodeInfo) bool {
	if ni == nil {
		return true
	}

	for _, task := range ni.Tasks {
		if IsBizNamespace(task.Namespace) {
			return false
		}
	}
	return true
}

// coreReq should in cores, not support multi cards query, that is <= 4
func NodeIdleDivideTecoCoreReq(ni *api.NodeInfo, coreReq uint) uint {
	if ni == nil || ni.Others[teco.DeviceName] == nil {
		return 0
	}
	tecoDevices := ni.Others[teco.DeviceName].(*teco.TecoDevices)
	if tecoDevices == nil || len(tecoDevices.Device) == 0 {
		return 0
	}
	if coreReq == 0 {
		return math.MaxUint
	}

	res := uint(0)
	for _, device := range tecoDevices.Device {
		remainMask := device.CoreUnMasked - device.CoreMask
		remains := getCoresCntFromMask(remainMask)
		res += remains / coreReq
	}
	return res
}

func getCoresCntFromMask(mask uint) uint {
	count := uint(0)
	for mask > 0 {
		count += mask % 2
		mask /= 2
	}
	return count
}
