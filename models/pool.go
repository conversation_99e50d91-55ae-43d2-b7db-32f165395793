package models

import (
	"fmt"
	"strings"

	billingPb "git-plat.tecorigin.net/ai-platform/billing-server/api/billing/v1"
)

type ResourcePoolType int32

const (
	ResourcePoolTypeUnknown ResourcePoolType = iota
	ResourcePoolTypeReserved
	ResourcePoolTypeDedicated
	ResourcePoolTypeShared

	ResourcePoolTypeCnt
)

func String2ResourcePoolType(value string) ResourcePoolType {
	iValue := strings.ToLower(value)
	switch iValue {
	case "reserved":
		return ResourcePoolTypeReserved
	case "dedicated":
		return ResourcePoolTypeDedicated
	case "shared":
		return ResourcePoolTypeShared
	}
	return ResourcePoolTypeUnknown
}

func ResourceMode2ResourcePoolType(value string) ResourcePoolType {
	iValue := strings.ToLower(value)
	switch iValue {
	case "purchasedresources":
		return ResourcePoolTypeDedicated
	case "requestcontainerresources":
		return ResourcePoolTypeShared
	}
	return ResourcePoolTypeUnknown
}

func ParsePoolTypeFromMap(labels map[string]string) ResourcePoolType {
	if labels == nil {
		return ResourcePoolTypeUnknown
	}
	return String2ResourcePoolType(labels[ResourcePoolLabelKey])
}

func ParsePoolTypeFromProductForm(form billingPb.ProductForm) ResourcePoolType {
	switch form {
	case billingPb.ProductForm_PRODUCT_FORM_K8S:
		return ResourcePoolTypeShared
	case billingPb.ProductForm_PRODUCT_FORM_SERVER:
		return ResourcePoolTypeDedicated
	default:
		return ResourcePoolTypeUnknown
	}
}

func (typeV ResourcePoolType) String() string {
	switch typeV {
	case ResourcePoolTypeUnknown:
		return "unknown"
	case ResourcePoolTypeReserved:
		return "reserved"
	case ResourcePoolTypeDedicated:
		return "dedicated"
	case ResourcePoolTypeShared:
		return "shared"
	}
	return fmt.Sprintf("ResourcePoolType(%d)", typeV)
}

func (typeV ResourcePoolType) ToK8sNsName() string {
	switch typeV {
	case ResourcePoolTypeUnknown:
		return "unknown"
	case ResourcePoolTypeReserved:
		return ResourcePoolTypeReservedK8sNs
	case ResourcePoolTypeDedicated:
		return ResourcePoolTypeDedicatedK8sNs
	case ResourcePoolTypeShared:
		return ResourcePoolTypeSharedK8sNs
	default:
		return fmt.Sprintf("ResourcePoolType(%d)", typeV)
	}
}

func ParsePoolIdFromMap(labels map[string]string) string {
	if labels == nil {
		return ""
	}
	return labels[ResourcePoolIdLabelKey]
}
