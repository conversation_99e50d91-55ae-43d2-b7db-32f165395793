package models

import (
	"fmt"
	"math"
	"strconv"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	quotav1 "k8s.io/apiserver/pkg/quota/v1"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	v1 "git-plat.tecorigin.net/ai-platform/console-backend/api/enum/v1"
	vo2 "git-plat.tecorigin.net/ai-platform/console-backend/api/vo/v2"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
)

type Cpu struct {
	Num          int64  `json:"num,omitempty"` // 1000, one core.
	Architecture string `json:"architecture,omitempty"`
}

type QuotaValue struct {
	Cpu              *Cpu                            `json:"cpu,omitempty"`
	Memory           uint64                          `json:"memory,omitempty"`
	ExtendedResource map[ExtendedResourceType]uint64 `json:"extended_resource,omitempty"`
}

func NewQuotaValueFromResource(resource *schedulingapi.Resource) *QuotaValue {
	if resource == nil {
		return nil
	}

	res := &QuotaValue{
		Cpu: &Cpu{
			Num: int64(resource.MilliCPU),
		},
		Memory:           uint64(resource.Memory),
		ExtendedResource: map[ExtendedResourceType]uint64{},
	}

	for rName, v := range resource.ScalarResources {
		if rName != corev1.ResourcePods {
			v /= 1000
		}
		erType := K8sResourceName2ExtendedResourceType(rName)
		res.ExtendedResource[erType] = uint64(v)
	}
	return res
}

func NewQuotaValueFromK8sResourceList(resources corev1.ResourceList) *QuotaValue {
	if resources == nil {
		return nil
	}

	cpu := resources.Name(corev1.ResourceCPU, resource.DecimalSI).MilliValue()
	cpuRequest := resources.Name(corev1.ResourceRequestsCPU, resource.DecimalSI).MilliValue()
	cpuLimit := resources.Name(corev1.ResourceLimitsCPU, resource.DecimalSI).MilliValue()
	mem := resources.Name(corev1.ResourceMemory, resource.BinarySI).Value()
	memRequest := resources.Name(corev1.ResourceRequestsMemory, resource.BinarySI).Value()
	memLimit := resources.Name(corev1.ResourceLimitsMemory, resource.BinarySI).Value()
	res := &QuotaValue{
		Cpu: &Cpu{
			Num: max(cpu, cpuRequest, cpuLimit),
		},
		Memory:           uint64(max(mem, memRequest, memLimit)),
		ExtendedResource: map[ExtendedResourceType]uint64{},
	}

	for k, v := range resources {
		switch k {
		case corev1.ResourceCPU:
			fallthrough
		case corev1.ResourceLimitsCPU:
			fallthrough
		case corev1.ResourceRequestsCPU:
			continue
		case corev1.ResourceMemory:
			fallthrough
		case corev1.ResourceRequestsMemory:
			fallthrough
		case corev1.ResourceLimitsMemory:
			continue
		default:
			erType := K8sResourceName2ExtendedResourceType(k)
			res.ExtendedResource[erType] = uint64(v.Value())
		}
	}
	return res
}

func (qv *QuotaValue) ToK8sResourceList() corev1.ResourceList {
	if qv == nil {
		return nil
	}

	res := corev1.ResourceList{
		corev1.ResourceCPU: *resource.NewMilliQuantity(
			qv.Cpu.Num, resource.DecimalSI),
		corev1.ResourceMemory: *resource.NewQuantity(
			int64(qv.Memory), resource.DecimalSI),
	}

	for name, v := range qv.ExtendedResource {
		resourceName := corev1.ResourceName(name.K8sResourceName())
		res[resourceName] = *resource.NewScaledQuantity(int64(v), 0)
	}

	return res
}

func (qv *QuotaValue) ToQuotaItems() []*vo2.QuotaItem {
	res := make([]*vo2.QuotaItem, 0)
	cpuItem := &vo2.QuotaItem{
		Type: v1.QuotaType_QUOTA_TYPE_CPU,
		Unit: v1.QuotaUnit_QUOTA_UNIT_CPU_MILLI_CORE,
		Used: qv.Cpu.Num,
	}
	res = append(res, cpuItem)

	memItem := &vo2.QuotaItem{
		Type: v1.QuotaType_QUOTA_TYPE_MEMORY,
		Unit: v1.QuotaUnit_QUOTA_UNIT_BYTE,
		Used: int64(qv.Memory),
	}
	res = append(res, memItem)

	for k, v := range qv.ExtendedResource {
		item := &vo2.QuotaItem{
			Type: k.ToUserQuotaType(),
			Unit: v1.QuotaUnit_QUOTA_UNIT_GPU_CARD,
			Used: int64(v),
		}
		res = append(res, item)
	}
	return res
}

// Divide returns how many instances of other can fit within this quota value.
// Returns:
//   - int64: The number of instances that can fit (-1 means unlimited)
//   - string: The name of the resource that is the limiting factor
func (qv *QuotaValue) Divide(other *QuotaValue) (int64, string) {
	if qv == nil {
		return 0, "none"
	}
	if other == nil {
		return -1, "none"
	}

	var cnt int64 = math.MaxInt64
	res := "cpu"
	if other.Cpu.Num > 0 {
		cnt = int64(qv.Cpu.Num / other.Cpu.Num)
	}

	var memCnt int64 = math.MaxInt64
	if other.Memory > 0 {
		memCnt = int64(qv.Memory / other.Memory)
	}
	if memCnt < cnt {
		cnt = memCnt
		res = "memory"
	}

	for erType, value := range qv.ExtendedResource {
		if erType == ExtendedResourceUnknown {
			continue
		}
		if other.ExtendedResource[erType] > 0 {
			tempCnt := int64(value / other.ExtendedResource[erType])
			if tempCnt < cnt {
				cnt = tempCnt
				res = erType.String()
			}
		}
	}

	if cnt == math.MaxInt64 {
		cnt = -1
	}
	return cnt, res
}

func (qv *QuotaValue) MultiER() bool {
	if qv == nil {
		return false
	}
	erCnt := 0
	for erType, value := range qv.ExtendedResource {
		if erType == ExtendedResourceUnknown {
			continue
		}
		if value > 0 {
			erCnt++
		}
	}
	return erCnt > 1
}

type Quota struct {
	Name        string
	TenantId    string
	WorkspaceId string
	Type        ResourcePoolType
	Value       *QuotaValue
}

func NewZeroQuota(tid, wid string, poolType ResourcePoolType) *Quota {
	res := &Quota{
		Type:        poolType,
		TenantId:    tid,
		WorkspaceId: wid,
		Value: &QuotaValue{
			Cpu: &Cpu{
				Num: 0,
			},
			Memory:           0,
			ExtendedResource: map[ExtendedResourceType]uint64{},
		},
	}

	for erName := range KnownER {
		erType := K8sResourceName2ExtendedResourceType(corev1.ResourceName(erName))
		res.Value.ExtendedResource[erType] = 0
	}
	return res
}

func NewMaxQuota(tid, wid string, poolType ResourcePoolType) *Quota {
	res := &Quota{
		Type:        poolType,
		TenantId:    tid,
		WorkspaceId: wid,
		Value: &QuotaValue{
			Cpu: &Cpu{
				Num: math.MaxInt64,
			},
			Memory:           math.MaxInt64,
			ExtendedResource: map[ExtendedResourceType]uint64{},
		},
	}

	for erName := range KnownER {
		erType := K8sResourceName2ExtendedResourceType(corev1.ResourceName(erName))
		res.Value.ExtendedResource[erType] = math.MaxInt64
	}
	return res
}

func (quota *Quota) ToK8sResourceQuota() *corev1.ResourceQuota {
	var ns string
	if quota.WorkspaceId != "" {
		ns = GetK8sNamespace(quota.Type, WorkspaceSegsInK8sNs, quota.WorkspaceId)
	} else {
		ns = GetK8sNamespace(quota.Type, TenantSegsInK8sNs, quota.TenantId)
	}

	cpuLimit := *resource.NewMilliQuantity(
		int64(quota.Value.Cpu.Num), resource.DecimalSI)
	memLimit := *resource.NewQuantity(
		int64(quota.Value.Memory), resource.DecimalSI)
	res := &corev1.ResourceQuota{
		ObjectMeta: metav1.ObjectMeta{
			Name:      ResourceQuotaName,
			Namespace: ns,
		},
		Spec: corev1.ResourceQuotaSpec{
			Hard: corev1.ResourceList{
				corev1.ResourceLimitsCPU:      cpuLimit,
				corev1.ResourceRequestsCPU:    cpuLimit,
				corev1.ResourceLimitsMemory:   memLimit,
				corev1.ResourceRequestsMemory: memLimit,
			},
		},
	}
	for name, value := range quota.Value.ExtendedResource {
		requestName := fmt.Sprintf("%s%s",
			ResourceRequestsPrefix, name.K8sResourceName())
		res.Spec.Hard[corev1.ResourceName(requestName)] =
			*resource.NewScaledQuantity(int64(value), 0)
	}

	return res
}

func (quota *Quota) ToK8sHierarchicalQuota() *hnc.HierarchicalResourceQuota {
	k8sQuota := quota.ToK8sResourceQuota()

	res := &hnc.HierarchicalResourceQuota{
		ObjectMeta: metav1.ObjectMeta{
			Name:      ResourceHierarchicalQuotaName,
			Namespace: k8sQuota.Namespace,
		},
		Spec: hnc.HierarchicalResourceQuotaSpec{
			Hard: k8sQuota.Spec.Hard,
		},
	}

	return res
}

func K8sQuotaToQuotaUsageMsg(quota *corev1.ResourceQuota) (
	string, interface{}, error) {

	if quota == nil {
		return "", nil, fmt.Errorf("nil quota when convert to quotaMsg.")
	}

	hierarchy, idStr, poolType, err := ParseK8sNamespace(quota.Namespace)
	if err != nil {
		return "", nil, err
	}
	if poolType != ResourcePoolTypeDedicated {
		return "", nil, fmt.Errorf(
			"quota namespace %s is not a dedicated pool", quota.Namespace)
	}

	if len(quota.Status.Used) == 0 {
		return "", nil, fmt.Errorf(
			"nil quota status.used when convert to quotaMsg.")
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return "", nil, err
	}

	var res interface{}
	var topic string
	switch hierarchy {
	case TenantSegsInK8sNs:
		topic = tecons.NatsSubjectRMTenantQuotaUsageChanged
		res = &tecmq.RMMsgTenantQuota{
			TenantID: id,
			Quotas:   ResourceListToQuotaItems(quota.Status.Used),
		}
	case WorkspaceSegsInK8sNs:
		topic = tecons.NatsSubjectRMWorkspaceQuotaUsageChanged
		res = &tecmq.RMMsgWorkspaceQuota{
			WorkspaceID: id,
			Quotas:      ResourceListToQuotaItems(quota.Status.Used),
		}
	default:
		return "", nil, fmt.Errorf(
			"unknown hierarchy %s to format usage mesage: %s/%s",
			hierarchy, quota.Namespace, quota.Name)
	}

	return topic, res, nil
}

func ResourceListToQuotaItems(resources corev1.ResourceList) []*vo2.QuotaItem {
	if resources == nil {
		return nil
	}

	qValue := NewQuotaValueFromK8sResourceList(resources)
	return qValue.ToQuotaItems()
}

func ResourceListChanged(oldResources, newResources corev1.ResourceList) bool {
	oldSet := sets.New(quotav1.ResourceNames(oldResources)...)
	newSet := sets.New(quotav1.ResourceNames(newResources)...)

	if !oldSet.Equal(newSet) {
		return true
	}

	for k, v := range oldResources {
		n := newResources[k]
		if !n.Equal(v) {
			return true
		}
	}

	return false
}
