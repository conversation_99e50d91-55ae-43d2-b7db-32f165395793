package models

import (
	"fmt"
	"math"

	v1 "git-plat.tecorigin.net/ai-platform/console-backend/api/enum/v1"
	vo2 "git-plat.tecorigin.net/ai-platform/console-backend/api/vo/v2"
	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	pb "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"

	"github.com/spf13/cast"
	corev1 "k8s.io/api/core/v1"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"
)

type TypedUsage map[CardModel]uint64

type QuotaTypedUsage struct {
	// cpu in milli core
	Cpu TypedUsage `json:"cpu,omitempty"`
	// memory in bytes
	Memory uint64 `json:"memory,omitempty"`
	// extended resource
	// value should in cores when erType is ExtendedResourceTeco
	ExtendedResource map[ExtendedResourceType]TypedUsage `json:"extended_resource,omitempty"`
}

func NewEmptyQuotaTypedUsage() *QuotaTypedUsage {
	// set cpu and memorty to 0
	res := &QuotaTypedUsage{
		Cpu:              map[CardModel]uint64{CardModel_Unknown: 0},
		Memory:           0,
		ExtendedResource: make(map[ExtendedResourceType]TypedUsage),
	}

	// set known card models to 0
	var i int32
	for i = 1; i < int32(CardModel_Cnt); i++ {
		erType := CardModel(i).ExtendedResourceType()
		typedUsage, ok := res.ExtendedResource[erType]
		if !ok {
			typedUsage = map[CardModel]uint64{}
			res.ExtendedResource[erType] = typedUsage
		}
		typedUsage[CardModel(i)] = 0
	}

	// set unknown card model to 0 of known er
	for erName := range KnownER {
		erType := K8sResourceName2ExtendedResourceType(corev1.ResourceName(erName))
		typedUsage, ok := res.ExtendedResource[erType]
		if !ok {
			typedUsage = map[CardModel]uint64{}
			res.ExtendedResource[erType] = typedUsage
		}
		typedUsage[CardModel_Unknown] = 0
	}
	return res
}

func NewQuotaTypedUsageFromResreq(
	resreq *schedulingapi.Resource, model CardModel) *QuotaTypedUsage {

	res := NewEmptyQuotaTypedUsage()
	res.Cpu[CardModel_Unknown] += uint64(resreq.MilliCPU)
	res.Memory += uint64(resreq.Memory)

	for rName, rQuant := range resreq.ScalarResources {
		if rName != corev1.ResourcePods {
			rQuant /= 1000
		}
		erType := K8sResourceName2ExtendedResourceType(rName)
		if erType == ExtendedResourceUnknown {
			continue
		}
		res.ExtendedResource[erType][model] += uint64(rQuant)
	}
	return res
}

func NewQuotaTypedUsageFromSpecConfiguration(
	configurations []*pb.SpecConfiguration) (*QuotaTypedUsage, error) {

	res := NewEmptyQuotaTypedUsage()

	for _, config := range configurations {
		if config.Name == "cpu" {
			res.Cpu[CardModel_Unknown] += cast.ToUint64(config.Value) * 1000
			continue
		}

		if config.Name == "memory" {
			memory, err := getBytes(config.Unit)
			if err != nil {
				return nil, errV1.ErrorInvalidParams("invalid unit: %s", config.Unit)
			}
			res.Memory += cast.ToUint64(config.Value) * memory
			continue
		}

		if !SpecERNamePattern.MatchString(config.Name) {
			return nil, errV1.ErrorInvalidParams(
				"invalid extended resource name: %s", config.Name)
		}
		matches := SpecERNamePattern.FindStringSubmatch(config.Name)
		if len(matches) != 3 {
			return nil, errV1.ErrorInvalidParams(
				"invalid extended resource name: %s", config.Name)
		}

		vendor, modelStr := matches[1], matches[2]
		erType := Vendor2ExtendedResourceType(vendor)
		if erType == ExtendedResourceUnknown {
			return nil, errV1.ErrorInvalidParams(
				"invalid extended resource vendor: %s", vendor)
		}
		modelStr = fmt.Sprintf("%s-%s", vendor, modelStr)
		cardModel := StringToCardModel(modelStr)
		res.ExtendedResource[erType][cardModel] += cast.ToUint64(config.Value)
		if erType == ExtendedResourceTeco {
			res.ExtendedResource[erType][cardModel] *= uint64(cardModel.Cores())
		}
	}

	return res, nil
}

func (usage *QuotaTypedUsage) Add(other *QuotaTypedUsage) {
	if other == nil {
		return
	}

	// Add CPU usage
	for model, value := range other.Cpu {
		usage.Cpu[model] += value
	}

	// Add Memory usage
	usage.Memory += other.Memory

	// Add Extended Resource usage
	for erType, typeUsage := range other.ExtendedResource {
		usageMap, ok := usage.ExtendedResource[erType]
		if !ok {
			usageMap = make(TypedUsage)
			usage.ExtendedResource[erType] = usageMap
		}
		for model, value := range typeUsage {
			usageMap[model] += value
		}
	}
}

func (usage *QuotaTypedUsage) Sub(other *QuotaTypedUsage) {
	if other == nil {
		return
	}

	// Subtract CPU usage
	for model, value := range other.Cpu {
		usage.Cpu[model] -= value
	}

	// Subtract Memory usage
	usage.Memory -= other.Memory

	// Subtract Extended Resource usage
	for erType, typeUsage := range other.ExtendedResource {
		usageMap, ok := usage.ExtendedResource[erType]
		if !ok {
			usageMap = make(TypedUsage)
			usage.ExtendedResource[erType] = usageMap
		}
		for model, value := range typeUsage {
			usageMap[model] -= value
		}
	}
}

// unit(card or core) is consistent with the usage.
func (usage *QuotaTypedUsage) ToQuotaItems() []*vo2.QuotaItem {
	res := make([]*vo2.QuotaItem, 0)
	cpuItem := &vo2.QuotaItem{
		Type: v1.QuotaType_QUOTA_TYPE_CPU,
		Unit: v1.QuotaUnit_QUOTA_UNIT_CPU_MILLI_CORE,
		Used: int64(usage.Cpu[CardModel_Unknown]),
	}
	res = append(res, cpuItem)

	memItem := &vo2.QuotaItem{
		Type: v1.QuotaType_QUOTA_TYPE_MEMORY,
		Unit: v1.QuotaUnit_QUOTA_UNIT_BYTE,
		Used: int64(usage.Memory),
	}
	res = append(res, memItem)

	cardCnter := make(map[v1.QuotaType]uint64)
	for erType, typeUsage := range usage.ExtendedResource {
		for mode, value := range typeUsage {
			// teco cards.
			if erType == ExtendedResourceTeco {
				if mode == CardModel_Unknown {
					// cores
					item := &vo2.QuotaItem{
						Type: v1.QuotaType_QUOTA_TYPE_GPU_TECO_CORE_CLUSTER,
						Unit: v1.QuotaUnit_QUOTA_UNIT_GPU_CORE_CLUSTER,
						Used: int64(value),
					}
					res = append(res, item)
					continue
				}
				item := &vo2.QuotaItem{
					Type: mode.ToQuotaType(),
					Unit: v1.QuotaUnit_QUOTA_UNIT_GPU_CARD,
					Used: int64(value) / int64(mode.Cores()),
				}
				cardCnter[erType.ToUserQuotaType()] += uint64(item.Used)
				res = append(res, item)
				continue
			}

			// other card
			if mode == CardModel_Unknown {
				item := &vo2.QuotaItem{
					Type: mode.ToQuotaType(),
					Unit: v1.QuotaUnit_QUOTA_UNIT_UNSPECIFIED,
					Used: int64(value),
				}
				res = append(res, item)
				continue
			}
			item := &vo2.QuotaItem{
				Type: mode.ToQuotaType(),
				Unit: v1.QuotaUnit_QUOTA_UNIT_GPU_CARD,
				Used: int64(value),
			}
			cardCnter[erType.ToUserQuotaType()] += uint64(item.Used)
			res = append(res, item)
		}
	}

	// add teco cards total number
	for t, v := range cardCnter {
		item := &vo2.QuotaItem{
			Type: t,
			Unit: v1.QuotaUnit_QUOTA_UNIT_GPU_CARD,
			Used: int64(v),
		}
		res = append(res, item)
	}
	return res
}

func (usage *QuotaTypedUsage) ToQuotaValue() *QuotaValue {
	res := &QuotaValue{
		Cpu: &Cpu{
			Num: int64(usage.Cpu[CardModel_Unknown]),
		},
		Memory:           usage.Memory,
		ExtendedResource: make(map[ExtendedResourceType]uint64),
	}

	for erType, typeUsage := range usage.ExtendedResource {
		var sum uint64 = 0
		for _, value := range typeUsage {
			sum += value
		}
		res.ExtendedResource[erType] = sum
	}
	return res
}

func (usage *QuotaTypedUsage) Divide(other *QuotaTypedUsage) (int64, string) {
	if usage == nil {
		return 0, "none"
	}
	if other == nil {
		return -1, "none"
	}

	var cnt int64 = math.MaxInt64
	res := "memory"
	if other.Memory > 0 {
		cnt = int64(usage.Memory / other.Memory)
	}

	for cpuType, value := range usage.Cpu {
		if other.Cpu[cpuType] > 0 {
			cpuCnt := int64(value / other.Cpu[cpuType])
			if cpuCnt < cnt {
				cnt = cpuCnt
				res = "cpu"
			}
		}
	}

	for erType, typeUsage := range usage.ExtendedResource {
		otherTypeUsage, ok := other.ExtendedResource[erType]
		if !ok {
			continue
		}

		for model, value := range otherTypeUsage {
			if value <= 0 {
				continue
			}
			erCnt := int64(typeUsage[model] / value)
			if erCnt < cnt {
				cnt = erCnt
				res = erType.String()
			}
		}
	}
	return cnt, res
}

func (usage *QuotaTypedUsage) Equal(other *QuotaTypedUsage) bool {
	if usage == nil && other == nil {
		return true
	}
	if usage == nil || other == nil {
		return false
	}
	if usage.Memory != other.Memory {
		return false
	}

	for cpuType, value := range usage.Cpu {
		if other.Cpu[cpuType] != value {
			return false
		}
	}
	for cpuType, value := range other.Cpu {
		if usage.Cpu[cpuType] != value {
			return false
		}
	}

	for erType, typeUsage := range usage.ExtendedResource {
		sum := uint64(0)
		for _, value := range typeUsage {
			sum += value
		}
		otherTypeUsage, ok := other.ExtendedResource[erType]
		if !ok {
			if sum == 0 {
				continue
			} else {
				return false
			}
		}
		// compare with other
		for model, value := range typeUsage {
			if otherTypeUsage[model] != value {
				return false
			}
		}
	}

	for erType, typeUsage := range other.ExtendedResource {
		sum := uint64(0)
		for _, value := range typeUsage {
			sum += value
		}
		selfTypeUsage, ok := usage.ExtendedResource[erType]
		if !ok {
			if sum == 0 {
				continue
			} else {
				return false
			}
		}
		for model, value := range typeUsage {
			if selfTypeUsage[model] != value {
				return false
			}
		}
	}

	return true
}

func (usage *QuotaTypedUsage) IsTecoCoreUsage() bool {
	if usage == nil || len(usage.ExtendedResource) == 0 {
		return false
	}
	if _, ok := usage.ExtendedResource[ExtendedResourceTeco]; !ok {
		return false
	}
	unknownTypeUsage := usage.ExtendedResource[ExtendedResourceTeco][CardModel_Unknown]
	return unknownTypeUsage > 0
}
