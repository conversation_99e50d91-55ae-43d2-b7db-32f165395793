package models

import (
	"fmt"
	"strconv"

	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecons"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
)

// HrqToQuotaUsageMsg converts a HierarchicalResourceQuota to a quota usage message.
//
// Parameters:
//   - hrq: The HierarchicalResourceQuota object to convert
//
// Returns:
//   - string: The topic name for publishing the message
//   - interface{}: The message payload (either RMMsgTenantQuota or RMMsgWorkspaceQuota)
//   - error: Error if conversion fails
func HrqToQuotaUsageMsg(hrq *hnc.HierarchicalResourceQuota) (
	string, interface{}, error) {

	if hrq == nil {
		return "", nil, fmt.Errorf("nil hrq when convert to quotaMsg.")
	}
	if len(hrq.Status.Used) == 0 {
		return "", nil, fmt.Errorf(
			"nil hrq status.used when convert to quotaMsg.")
	}
	hierarchy, idStr, poolType, err := ParseK8sNamespace(hrq.Namespace)
	if err != nil {
		return "", nil, err
	}
	if poolType != ResourcePoolTypeDedicated && poolType != ResourcePoolTypeShared {
		return "", nil, fmt.Errorf(
			"hrq namespace %s is not dedicated or ondemand pool", hrq.Namespace)
	}
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return "", nil, err
	}

	var res interface{}
	var topic string
	switch hierarchy {
	case TenantSegsInK8sNs:
		if poolType == ResourcePoolTypeDedicated {
			topic = tecons.NatsSubjectRMTenantQuotaUsageChanged
		} else {
			topic = tecons.NatsSubjectRMTenantOnDemandQuotaUsageChanged
		}
		res = &tecmq.RMMsgTenantQuota{
			TenantID: id,
			Quotas:   ResourceListToQuotaItems(hrq.Status.Used),
		}
	case WorkspaceSegsInK8sNs:
		if poolType == ResourcePoolTypeDedicated {
			topic = tecons.NatsSubjectRMWorkspaceQuotaUsageChanged
		} else {
			topic = tecons.NatsSubjectRMWorkspaceOnDemandQuotaUsageChanged
		}
		res = &tecmq.RMMsgWorkspaceQuota{
			WorkspaceID: id,
			Quotas:      ResourceListToQuotaItems(hrq.Status.Used),
		}
	default:
		return "", nil, fmt.Errorf(
			"unknown hierarchy %s to format usage mesage: %s/%s",
			hierarchy, hrq.Namespace, hrq.Name)
	}

	return topic, res, nil
}
