package models

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"
)

func model2FlatMap(modelItem interface{}) (map[string]interface{}, error) {
	jsonBytes, err := json.<PERSON>(modelItem)
	if err != nil {
		return nil, errors.Wrap(err, "Convert model to db item failed.")
	}
	var item map[string]interface{}

	// should not use json.Unmarshal directly. because json.Unmarshal will change
	// number (int and float) to float
	// solution: https://stackoverflow.com/a/22346593/1761540
	decoder := json.NewDecoder(strings.NewReader(string(jsonBytes[:])))
	decoder.UseNumber()
	err = decoder.Decode(&item)
	if err != nil {
		return nil, errors.Wrap(err, "Convert model to map failed.")
	}
	return item, nil
}

func map2Model(mapItem interface{}, modelItem interface{}) error {
	jsonBytes, err := json.<PERSON>(mapItem)
	if err != nil {
		return errors.Wrap(err, "Convert map to model failed.")
	}

	decoder := json.NewDecoder(strings.NewReader(string(jsonBytes[:])))
	decoder.UseNumber()
	err = decoder.Decode(&modelItem)
	if err != nil {
		return errors.Wrap(err, "Convert model to map failed.")
	}
	return nil
}

func model2OrcaAPIMap(modelItem interface{}) (map[string]interface{}, error) {

	orcaAPIMap, err := model2FlatMap(modelItem)
	if err != nil {
		return nil, err
	}

	subKeys := []string{"base", "resource_info", "resource_desc"}
	err = flattenSubMap(&orcaAPIMap, subKeys)
	if err != nil {
		return nil, err
	}

	orcaUniqueID, err := popMapKey(&orcaAPIMap, "unique_id")
	if err != nil {
		return nil, err
	}
	orcaAPIMap["orca_id"] = orcaUniqueID

	return orcaAPIMap, nil
}

func flattenSubMap(
	targetMap *map[string]interface{}, subKeyList []string) error {

	for _, subKey := range subKeyList {
		subMapI, err := popMapKey(targetMap, subKey)
		if err != nil {
			return err
		}
		subMap, ok := subMapI.(map[string]interface{})
		if !ok {
			return errors.New("Not a map.")
		}
		for subMapKey, subMapValue := range subMap {
			(*targetMap)[subMapKey] = subMapValue
		}
	}
	return nil
}

func popMapKey(targetMap *map[string]interface{}, key string) (
	interface{}, error) {

	if len(key) == 0 {
		return nil, errors.New("key is empty when pop key.")
	}

	value, ok := (*targetMap)[key]
	if !ok {
		errMsg := fmt.Sprintf("key |%s| not found in map.", key)
		return nil, errors.New(errMsg)
	}
	delete(*targetMap, key)
	return value, nil
}
