package models

import (
	"fmt"
	"regexp"

	"github.com/imdario/mergo"
	corev1 "k8s.io/api/core/v1"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
)

// namespace pattern
var K8sNsPattern = regexp.MustCompile(fmt.Sprintf(
	`^%s-(%s|%s|%s)-(\d+)(-(dedicated|shared|reserved))?$`,
	ResourcePoolK8sNsPrefix,
	TenantSegsInK8sNs, WorkspaceSegsInK8sNs, ProjectSegsInK8sNs))

func GetK8sNamespace(pool ResourcePoolType, hierarchy, id string) string {
	switch pool {
	case ResourcePoolTypeReserved:
		fallthrough
	case ResourcePoolTypeShared:
		return fmt.Sprintf("%s-%s-%s-%s",
			ResourcePoolK8sNsPrefix, hierarchy, id, pool.String())
	case ResourcePoolTypeDedicated:
		return fmt.Sprintf("%s-%s-%s",
			ResourcePoolK8sNsPrefix, hierarchy, id)
	default:
		return ""
	}
}

func IsBizNamespace(ns string) bool {
	return K8sNsPattern.MatchString(ns)
}

func ParseK8sNamespace(ns string) (
	hierarchy, id string, poolType ResourcePoolType, err error) {

	invalidErr := fmt.Errorf("Invalid k8s namespace: %s", ns)
	if !IsBizNamespace(ns) {
		return "", "", ResourcePoolTypeUnknown, invalidErr
	}

	matches := K8sNsPattern.FindStringSubmatch(ns)
	if len(matches) != 5 {
		return "", "", ResourcePoolTypeUnknown, invalidErr
	}

	poolType = String2ResourcePoolType(matches[4])
	if poolType == ResourcePoolTypeUnknown {
		poolType = ResourcePoolTypeDedicated
	}

	hierarchy, id, err = matches[1], matches[2], nil
	return
}

func MergeK8sNamespace(orgin, new *corev1.Namespace) error {
	// bug maybe: cannot merge default value field, like 5 -> 0, "a" -> ""
	err := mergo.Merge(orgin, new, mergo.WithOverride)
	if err != nil {
		return errV1.ErrorUnknown(
			"Merge new k8s namespace to origin failed: %v", err)
	}
	return nil
}

func MergeK8sSubNs(orgin, new *hnc.SubnamespaceAnchor) error {
	// bug maybe: cannot merge default value field, like 5 -> 0, "a" -> ""
	err := mergo.Merge(orgin, new, mergo.WithOverride)
	if err != nil {
		return errV1.ErrorUnknown(
			"Merge new k8s subns to origin failed: %v", err)
	}
	return nil
}
