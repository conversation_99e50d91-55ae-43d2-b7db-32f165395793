package models

import (
	"git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
)

// NamespaceCollection will record all details about namespace
type NamespaceCollection struct {
	Name        string
	QuotaStatus map[string]v1alpha2.HierarchicalResourceQuotaStatus
}

// NewNamespaceCollection creates new NamespaceCollection object to record all information about a namespace
func NewNamespaceCollection(name string) *NamespaceCollection {
	n := &NamespaceCollection{
		Name:        name,
		QuotaStatus: make(map[string]v1alpha2.HierarchicalResourceQuotaStatus),
	}
	return n
}

// Update modify the registered information according quota object
func (n *NamespaceCollection) Update(quota *v1alpha2.HierarchicalResourceQuota) {
	n.QuotaStatus[quota.Name] = quota.Status
}

// Delete remove the registered information according quota object
func (n *NamespaceCollection) Delete(quota *v1alpha2.HierarchicalResourceQuota) {
	delete(n.Quota<PERSON>tatus, quota.Name)
}
