package models

import (
	"fmt"

	tenantPb "git-plat.tecorigin.net/ai-platform/console-backend/api/tenant/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
	schedulingapi "volcano.sh/volcano/pkg/scheduler/api"

	corev1 "k8s.io/api/core/v1"
)

type NodeSystemInfo struct {
	KernelVersion   string `json:"kernel_version"`
	OperatingSystem string `json:"os"`
	OSImage         string `json:"os_image"`
	Architecture    string `json:"architecture"`
}

type Node struct {
	Id             string            `json:"id"`
	Name           string            `json:"name"`
	IP             string            `json:"ip"`
	Type           NodeTypeEnum      `json:"node_type"`
	Label          map[string]string `json:"label"`
	NodeSystemInfo NodeSystemInfo    `json:"system_info"`
	Capacity       QuotaValue        `json:"resource_capacity"`
	Allocatable    QuotaValue        `json:"resource_allocatable"`
	Allocated      QuotaValue        `json:"resource_allocated"`
	Status         NodeStatusEnum    `json:"status"`

	PoolId   string           `json:"pool_id"`
	PoolType ResourcePoolType `json:"pool_type"`
	SpecId   string           `json:"spec_id"`

	*schedulingapi.NodeInfo `json:"-"`
}

func NewNodeFromMap(nodeMap map[string]interface{}) (*Node, error) {
	var node Node
	err := map2Model(nodeMap, &node)
	if err != nil {
		return nil, err
	}
	return &node, nil
}

func NewNodeFromK8sNode(nodeK8sObj *corev1.Node) *Node {
	var nodeIP string
	for _, addr := range nodeK8sObj.Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			nodeIP = addr.Address
			break
		}
	}

	var nodeStatus NodeStatusEnum
	if !nodeK8sObj.Spec.Unschedulable {
		nodeStatus = NodeSchedulable
	} else {
		nodeStatus = NodeUnschedulable
	}

	resourceCapacity := NewQuotaValueFromK8sResourceList(
		nodeK8sObj.Status.Capacity)
	resourceAllocatable := NewQuotaValueFromK8sResourceList(
		nodeK8sObj.Status.Allocatable)

	res := &Node{
		Id:     string(nodeK8sObj.UID),
		Name:   nodeK8sObj.Name,
		IP:     nodeIP,
		Label:  nodeK8sObj.Labels,
		Status: nodeStatus,
		Type:   ParseNodeTypeFromMap(nodeK8sObj.Labels),
		NodeSystemInfo: NodeSystemInfo{
			KernelVersion:   nodeK8sObj.Status.NodeInfo.KernelVersion,
			OperatingSystem: nodeK8sObj.Status.NodeInfo.OperatingSystem,
			OSImage:         nodeK8sObj.Status.NodeInfo.OSImage,
			Architecture:    nodeK8sObj.Status.NodeInfo.Architecture,
		},
		Capacity:    *resourceCapacity,
		Allocatable: *resourceAllocatable,
	}

	return res
}

func NewNodeFromListSupportNodesReply_One(
	node *tenantPb.ListSupportNodesReply_One) *Node {
	return &Node{
		Id:     node.Uuid,
		Name:   node.Name,
		PoolId: node.PoolUuid,
		SpecId: node.SpecId,
	}
}

func (node *Node) DataMap() (map[string]interface{}, error) {
	item, err := model2FlatMap(node)
	if err != nil {
		return nil, err
	}
	item["status"] = node.Status.String()
	item["node_type"] = node.Type.String()
	return item, nil
}

// AddResourceToHrq adds the node's resources to the Hierarchical Resource Quota (HRQ).
//
// Parameters:
// hrq: The Hierarchical Resource Quota to update.
func (node *Node) AddResourceToHrq(hrq *hnc.HierarchicalResourceQuota) {
	value := node.Capacity
	nodeRes := value.ToK8sResourceList()

	cpuQuant := nodeRes.Cpu()
	requestCpu := hrq.Spec.Hard[corev1.ResourceRequestsCPU]
	requestCpu.Add(*cpuQuant)
	hrq.Spec.Hard[corev1.ResourceRequestsCPU] = requestCpu
	limitCpu := hrq.Spec.Hard[corev1.ResourceLimitsCPU]
	limitCpu.Add(*cpuQuant)
	hrq.Spec.Hard[corev1.ResourceLimitsCPU] = limitCpu

	memQuant := nodeRes.Memory()
	reqquestMem := hrq.Spec.Hard[corev1.ResourceRequestsMemory]
	reqquestMem.Add(*memQuant)
	hrq.Spec.Hard[corev1.ResourceRequestsMemory] = reqquestMem
	limitMem := hrq.Spec.Hard[corev1.ResourceLimitsMemory]
	limitMem.Add(*memQuant)
	hrq.Spec.Hard[corev1.ResourceLimitsMemory] = limitMem

	for name := range KnownER {
		limitName := corev1.ResourceName(
			fmt.Sprintf("%s%s", ResourceRequestsPrefix, name))
		tQuant, ok := nodeRes[corev1.ResourceName(name)]
		if !ok {
			continue
		}
		nowQuant := hrq.Spec.Hard[limitName]
		nowQuant.Add(tQuant)
		hrq.Spec.Hard[limitName] = nowQuant
	}
}

// SubStractResourceToHrq subtracts the node's resources from the Hierarchical Resource Quota (HRQ).
//
// Parameters:
// hrq: The Hierarchical Resource Quota to update.
func (node *Node) SubStractResourceToHrq(hrq *hnc.HierarchicalResourceQuota) {
	value := node.Capacity
	nodeRes := value.ToK8sResourceList()

	cpuQuant := nodeRes.Cpu()
	requestCpu := hrq.Spec.Hard[corev1.ResourceRequestsCPU]
	requestCpu.Sub(*cpuQuant)
	hrq.Spec.Hard[corev1.ResourceRequestsCPU] = requestCpu
	limitCpu := hrq.Spec.Hard[corev1.ResourceLimitsCPU]
	limitCpu.Sub(*cpuQuant)
	hrq.Spec.Hard[corev1.ResourceLimitsCPU] = limitCpu

	memQuant := nodeRes.Memory()
	requestMem := hrq.Spec.Hard[corev1.ResourceRequestsMemory]
	requestMem.Sub(*memQuant)
	hrq.Spec.Hard[corev1.ResourceRequestsMemory] = requestMem
	limitMem := hrq.Spec.Hard[corev1.ResourceLimitsMemory]
	limitMem.Sub(*memQuant)
	hrq.Spec.Hard[corev1.ResourceLimitsMemory] = limitMem

	for name := range KnownER {
		limitName := corev1.ResourceName(fmt.Sprintf(
			"%s%s", ResourceRequestsPrefix, name))
		tQuant, ok := nodeRes[corev1.ResourceName(name)]
		if !ok {
			continue
		}
		nowQuant := hrq.Spec.Hard[limitName]
		nowQuant.Sub(tQuant)
		if nowQuant.Sign() < 0 {
			nowQuant.Set(0)
		}
		hrq.Spec.Hard[limitName] = nowQuant
	}
}
