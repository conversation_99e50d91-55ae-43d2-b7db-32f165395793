package models

import "strings"

type NodeStatusEnum uint8

const (
	NodeStatusUnknown NodeStatusEnum = iota
	NodeSchedulable
	NodeUnschedulable
)

func StringToNodeStatus(value string) NodeStatusEnum {
	switch strings.ToLower(value) {
	case "schedulable":
		return NodeSchedulable
	case "unschedulable":
		return NodeUnschedulable
	}
	return NodeStatusUnknown
}

func (value NodeStatusEnum) String() string {
	switch value {
	case NodeSchedulable:
		return "Schedulable"
	case NodeUnschedulable:
		return "Unschedulable"
	}
	return "Unknown"
}

type NodeTypeEnum uint8

const (
	NodeTypeUnknown NodeTypeEnum = iota
	ManagerAndComputeNode
	ManagerNode
	ComputeNode
)

func StringToOrcaNodeType(value string) NodeTypeEnum {
	switch strings.ToLower(value) {
	case "managerandcompute":
		return ManagerAndComputeNode
	case "manager":
		return ManagerNode
	case "compute":
		return ComputeNode
	}
	return NodeTypeUnknown
}

func ParseNodeTypeFromMap(labels map[string]string) NodeTypeEnum {
	isManager := IsManagerNode(labels)
	isCompute := IsComputeNode(labels)
	if isManager && isCompute {
		return ManagerAndComputeNode
	} else if isManager {
		return ManagerNode
	} else if isCompute {
		return ComputeNode
	} else {
		return NodeTypeUnknown
	}
}

func (value NodeTypeEnum) String() string {
	switch value {
	case ManagerAndComputeNode:
		return "ManagerAndCompute"
	case ManagerNode:
		return "Manager"
	case ComputeNode:
		return "Compute"
	}
	return "Unknown"
}

func IsManagerNode(labels map[string]string) bool {
	if labels == nil {
		return false
	}

	labelValue, ok := labels[NodeTypeManagerLabelKey]
	if !ok || labelValue != "yes" {
		return false
	}
	return true
}

func IsComputeNode(labels map[string]string) bool {
	if labels == nil {
		return false
	}

	labelValue, ok := labels[NodeTypeComputeLabelKey]
	if !ok || labelValue != "yes" {
		return false
	}
	return true
}
