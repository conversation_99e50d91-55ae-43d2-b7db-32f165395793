package models

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pb "git-plat.tecorigin.net/ai-platform/scheduling-manager/api/scheduling-manager/v1"
	hnc "git-plat.tecorigin.net/ai-platform/scheduling-manager/hnc/pkg/apis/hnc.x-k8s.io/v1alpha2"
)

// corresponding to workspace in business, not k8s.
type Workspace struct {
	Id       string `json:"id"`
	Name     string `json:"name"`
	TenantId string `json:"tenant_id"`

	// resource pool -> quota
	Quotas map[ResourcePoolType]*Quota `json:"quotas,omitempty"`
}

func NewWorkspaceFromPBCreateRequest(req *pb.CreateWorkspaceRequest) *Workspace {
	w := &Workspace{
		Id:       req.WorkspaceId,
		Name:     req.Name,
		TenantId: req.TenantId,
		Quotas:   make(map[ResourcePoolType]*Quota),
	}

	for poolType, quota := range req.Quotas {
		pool := ResourcePoolType(poolType)

		er := make(map[ExtendedResourceType]uint64, 0)
		for k, v := range quota.ExtendedResource {
			er[ExtendedResourceType(k)] = v
		}

		w.Quotas[pool] = &Quota{
			Name:        ResourceQuotaName,
			WorkspaceId: req.WorkspaceId,
			TenantId:    req.TenantId,
			Type:        pool,
			Value: &QuotaValue{
				Cpu: &Cpu{
					Num:          quota.Cpu.Num,
					Architecture: quota.Cpu.Architecture,
				},
				Memory:           quota.Memory,
				ExtendedResource: er,
			},
		}
	}

	return w
}

func NewWorkspaceFromPBRemoveRequest(req *pb.RemoveWorkspaceRequest) *Workspace {
	return &Workspace{
		Id:       req.WorkspaceId,
		TenantId: req.TenantId,
	}
}

func (w *Workspace) ToK8sNamespaces() map[ResourcePoolType]*corev1.Namespace {
	namespaces := make(map[ResourcePoolType]*corev1.Namespace)
	for i := 1; i < int(ResourcePoolTypeCnt); i++ {
		nsName := GetK8sNamespace(ResourcePoolType(i), WorkspaceSegsInK8sNs, w.Id)
		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: nsName,
				Labels: map[string]string{
					"workspace_id": w.Id,
				},
				Annotations: map[string]string{},
			},
		}
		if w.TenantId != "" {
			ns.Labels["tenant_id"] = w.TenantId
		}
		if w.Name != "" {
			ns.Annotations["workspace_name"] = w.Name
		}
		namespaces[ResourcePoolType(i)] = ns
	}
	return namespaces
}

func (w *Workspace) Tok8sSubNss() map[ResourcePoolType]*hnc.SubnamespaceAnchor {
	res := map[ResourcePoolType]*hnc.SubnamespaceAnchor{}
	for i := 1; i < int(ResourcePoolTypeCnt); i++ {
		pool := ResourcePoolType(i)
		if pool == ResourcePoolTypeReserved {
			continue
		}

		nsName := GetK8sNamespace(pool, WorkspaceSegsInK8sNs, w.Id)
		parent := GetK8sNamespace(pool, TenantSegsInK8sNs, w.TenantId)

		subns := &hnc.SubnamespaceAnchor{
			ObjectMeta: metav1.ObjectMeta{
				Name:        nsName,
				Namespace:   parent,
				Labels:      map[string]string{},
				Annotations: map[string]string{},
			},
			Spec: hnc.SubnamespaceAnchorSpec{
				Labels: []hnc.MetaKVP{
					{
						Key:   HncTenantLabel,
						Value: w.TenantId,
					},
					{
						Key:   HncWorkspaceLabel,
						Value: w.Id,
					},
				},
			},
		}

		if w.Name != "" {
			subns.Annotations[NSWorkspaceNameAnnotation] = w.Name
		}
		res[pool] = subns
	}
	return res
}

func (ws *Workspace) ToK8sQuotas() []*corev1.ResourceQuota {
	res := make([]*corev1.ResourceQuota, 0)
	for _, iQuota := range ws.Quotas {
		quota := iQuota.ToK8sResourceQuota()
		res = append(res, quota)
	}
	return res
}

func (ws *Workspace) ToK8sHierarchicalQuotas() []*hnc.HierarchicalResourceQuota {
	res := make([]*hnc.HierarchicalResourceQuota, 0)
	for _, iQuota := range ws.Quotas {
		quota := iQuota.ToK8sHierarchicalQuota()
		res = append(res, quota)
	}
	return res
}

func (ws *Workspace) ToK8sHierarchicalQuotaMap() map[ResourcePoolType]*hnc.HierarchicalResourceQuota {
	hrqMap := map[ResourcePoolType]*hnc.HierarchicalResourceQuota{}
	for poolType, iQuota := range ws.Quotas {
		quota := iQuota.ToK8sHierarchicalQuota()
		hrqMap[poolType] = quota
	}
	return hrqMap
}
